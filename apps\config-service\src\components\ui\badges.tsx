"use client";

import { cn } from "@/lib/utils";

interface ActiveStatusBadgeProps {
  isActive: boolean;
  className?: string;
}

export const ActiveStatusBadge = ({
  isActive,
  className,
}: ActiveStatusBadgeProps) => {
  return (
    <span
      className={cn(
        "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
        isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800",
        className,
      )}
    >
      {isActive ? "Active" : "Inactive"}
    </span>
  );
};

interface PillBadgeProps {
  variant?: "default" | "success" | "warning" | "error";
  className?: string;
  children: React.ReactNode;
}

export const PillBadge = ({
  variant = "default",
  className,
  children,
}: PillBadgeProps) => {
  const variantClasses = {
    default: "bg-gray-100 text-gray-800",
    success: "bg-green-100 text-green-800",
    warning: "bg-yellow-100 text-yellow-800",
    error: "bg-red-100 text-red-800",
  };

  return (
    <span
      className={cn(
        "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
        variantClasses[variant],
        className,
      )}
    >
      {children}
    </span>
  );
};
