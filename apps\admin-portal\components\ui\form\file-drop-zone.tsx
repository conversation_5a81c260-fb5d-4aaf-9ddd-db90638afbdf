import { useCallback } from "react";
import { FileRejection, useDropzone } from "react-dropzone";
import { useFormContext } from "react-hook-form";
import toast from "react-hot-toast";
import { FiFileText, FiPlus, FiTrash2, FiUploadCloud } from "react-icons/fi";
import { RiDeleteBinLine } from "react-icons/ri";

import { DOCUMENT_ICONS, DocumentType } from "@/components/icons/doc-icons";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

type Props = {
  acceptTypes?: string[];
  maxSizeMB?: number;
  name: string;
  shouldValidate?: boolean;
  multiple?: boolean;
};

const ERROR_MESSAGES = {
  "file-too-large":
    "The selected file is too large. Please upload a smaller file.",
  "file-invalid-type":
    "Invalid file type. Please upload a supported file format.",
};

export const FileDropzone = ({
  name,
  acceptTypes,
  maxSizeMB = 50,
  shouldValidate = true,
  multiple = false,
}: Props) => {
  const { setValue, watch } = useFormContext();
  const files = watch(name);

  const onDrop = useCallback(
    (acceptedFiles: File[], fileRejections: FileRejection[]) => {
      if (fileRejections.length > 0) {
        const errorCode = fileRejections[0]?.errors[0]?.code;
        const reason =
          ERROR_MESSAGES[errorCode as keyof typeof ERROR_MESSAGES] ??
          fileRejections[0]?.errors[0]?.message ??
          "Invalid file.";
        toast.error(reason);
        return;
      }

      if (acceptedFiles.length > 0) {
        if (multiple) {
          const existingFiles = Array.isArray(files) ? files : [];
          setValue(name, [...existingFiles, ...acceptedFiles], {
            shouldValidate: shouldValidate,
          });
        } else {
          setValue(name, acceptedFiles[0], {
            shouldValidate: shouldValidate,
          });
        }
      }
    },
    [name, setValue, shouldValidate, multiple, files],
  );

  const maxSizeBytes = maxSizeMB * 1024 * 1024;

  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop,
    noClick: true,
    noKeyboard: true,
    multiple,
    maxSize: maxSizeBytes,
    accept: acceptTypes?.length
      ? {
          "": acceptTypes,
        }
      : undefined,
  });

  const handleRemove = (index: number) => {
    if (multiple && Array.isArray(files)) {
      const filteredFiles = files.filter((_, idx) => idx !== index);
      setValue(name, filteredFiles, {
        shouldValidate,
      });
    } else {
      setValue(name, undefined, { shouldValidate });
    }
  };

  const handleClearAll = () => {
    setValue(name, multiple ? [] : undefined, { shouldValidate });
  };
  const hasFiles = multiple
    ? Array.isArray(files) && files.length > 0
    : !!files;

  return (
    <div
      {...getRootProps()}
      className={cn(
        "relative flex h-72 items-center  rounded-lg border-2 border-dashed p-3 transition-all duration-200 sm:p-6",
        isDragActive
          ? "border-blue-500 bg-blue-50 dark:bg-blue-950"
          : "border-gray-300 dark:border-gray-600",
      )}
    >
      <input {...getInputProps()} />

      {!hasFiles ? (
        <div className="flex-1 text-center ">
          <FiUploadCloud className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <div className="space-y-2">
            <p className="text-lg font-medium text-gray-900 dark:text-white">
              Drop file{multiple ? "s" : ""} here
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              or{" "}
              <button
                type="button"
                onClick={open}
                className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400"
              >
                browse to upload
              </button>
            </p>
          </div>
          <div className="mt-4 space-y-1 text-xs text-gray-500 dark:text-gray-400">
            <p>
              Supported formats:{" "}
              {acceptTypes?.length ? acceptTypes.join(", ") : "All file types"}
            </p>
            <p>Maximum size: {maxSizeMB}MB</p>
          </div>
        </div>
      ) : (
        <div className="flex h-full w-full flex-1 flex-col gap-y-3">
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">
              {multiple
                ? `${Array.isArray(files) ? files.length : 0} file${Array.isArray(files) && files.length !== 1 ? "s" : ""} selected`
                : "File selected"}
            </h4>
            <div className="flex justify-end gap-2">
              {multiple ? (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      open();
                    }}
                    className="border-primary-500 text-primary-500 hover:bg-primary-50 dark:border-primary-500 dark:text-primary-500 dark:hover:bg-primary-950 h-8 bg-white px-3 dark:bg-gray-800"
                  >
                    <FiPlus className="mr-1 h-3 w-3" />
                    Add More
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleClearAll();
                    }}
                    className="h-8 border-red-300 bg-white px-3 text-red-600 hover:bg-red-50 hover:text-red-700 dark:border-red-300 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-red-950"
                  >
                    <FiTrash2 className="mr-1 h-3 w-3" />
                    Clear All
                  </Button>
                </>
              ) : (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    open();
                  }}
                  className="border-primary-500 text-primary-500 hover:bg-primary-50 dark:border-primary-500 dark:text-primary-500 dark:hover:bg-primary-950 h-8 bg-white px-3 dark:bg-gray-800"
                >
                  Select Another
                </Button>
              )}
            </div>
          </div>

          <div
            className={cn(
              "flex flex-1 flex-col gap-2 overflow-y-auto",
              !Array.isArray(files) && "justify-center",
            )}
          >
            {multiple && Array.isArray(files) ? (
              files.map((file: File, index: number) => (
                <FileItem
                  key={file.name + index}
                  file={file}
                  onRemove={() => handleRemove(index)}
                />
              ))
            ) : files ? (
              <FileItem file={files} onRemove={() => handleRemove(0)} />
            ) : null}
          </div>
        </div>
      )}
    </div>
  );
};

type FileItemProps = {
  file: File;
  onRemove: () => void;
};

const FILE_ICON_VARIANTS = {
  pdf: "bg-red-100 text-red-500 dark:bg-red-900 dark:text-red-300",
  doc: "bg-blue-100 text-blue-500 dark:bg-blue-900 dark:text-blue-300",
  docx: "bg-blue-100 text-blue-500 dark:bg-blue-900 dark:text-blue-300",
  jpg: "bg-teal-100 text-teal-600 dark:bg-teal-900 dark:text-teal-300",
  jpeg: "bg-teal-100 text-teal-600 dark:bg-teal-900 dark:text-teal-300",
  png: "bg-teal-100 text-teal-600 dark:bg-teal-900 dark:text-teal-300",
  xls: "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300",
  xlsx: "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300",
  txt: "bg-orange-100 text-orange-500 dark:bg-orange-900 dark:text-orange-300",
  csv: "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300",
  eml: "bg-purple-100 text-purple-500 dark:bg-purple-900 dark:text-purple-300",
} as const;

const DEFAULT_VARIANT =
  "bg-blue-100 text-blue-500 dark:bg-blue-900 dark:text-blue-300";

const FileItem = ({ file, onRemove }: FileItemProps) => {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const extension = file.name.split(".").pop()?.toLowerCase() || "";
  const documentIcon = DOCUMENT_ICONS[extension as DocumentType];

  return (
    <div className="group relative rounded-xl border border-gray-200 bg-white p-4 shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-700 dark:bg-gray-800">
      <div className="flex items-start gap-2 sm:gap-4">
        <div
          className={cn(
            "flex size-10 flex-shrink-0 items-center justify-center rounded-lg sm:size-12",
            FILE_ICON_VARIANTS[extension as DocumentType] || DEFAULT_VARIANT,
          )}
        >
          {documentIcon || <FiFileText className="size-5" />}
        </div>

        <div className="flex min-w-0 flex-1 items-start justify-between gap-2">
          <div className="flex min-w-0 flex-1 flex-col">
            <h4 className="flex-1 truncate text-sm font-semibold text-gray-900 dark:text-white">
              {file.name}
            </h4>
            <div className="mt-1 flex items-center gap-2">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {formatFileSize(file.size)}
              </span>
              {extension && (
                <span
                  className={cn(
                    "rounded-full px-2 py-0.5 text-xs font-medium uppercase",
                    FILE_ICON_VARIANTS[extension as DocumentType] ||
                      DEFAULT_VARIANT,
                  )}
                >
                  {extension}
                </span>
              )}
            </div>
          </div>

          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onRemove();
            }}
            className="rounded-full p-1.5 text-gray-400 opacity-100 transition-opacity hover:bg-red-50 hover:text-red-500 group-hover:opacity-100 sm:opacity-0 dark:hover:bg-red-900/20"
            title="Remove file"
          >
            <RiDeleteBinLine className="size-5" />
          </button>
        </div>
      </div>
    </div>
  );
};
