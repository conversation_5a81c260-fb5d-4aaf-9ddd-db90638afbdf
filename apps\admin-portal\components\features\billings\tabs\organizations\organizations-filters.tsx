"use client";

import { capitalize } from "lodash";
import { ListFilter } from "lucide-react";
import { parseAsString } from "nuqs";
import { useQueryState } from "nuqs";
import { useRef, useState } from "react";
import { z } from "zod";

import { GROUP_INSTITUTION_TYPES } from "@/components/features/settings/groups/default/modal-add-group";
import { Button } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { Form, FormRef, InputField, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { SelectProvinces } from "@/components/ui/form/select/select-provinces";
import { cn } from "@/lib/utils";

const TYPE_OPTIONS = [
  { label: "Family", value: "family" },
  { label: "Site", value: "site" },
  { label: "CRO", value: "cro" },
  { label: "ARO", value: "aro" },
  { label: "Clincove", value: "clincove" },
];

const schema = z.object({
  name: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  stateProvince: z.string().optional().nullable(),
  type: z.string().optional().nullable(),
  groupInstitutionType: z.string().optional().nullable(),
});

const useOrganizationsFilters = () => {
  const [type, setType] = useQueryState("type", parseAsString);
  const [groupInstitutionType, setGroupInstitutionType] = useQueryState(
    "groupInstitutionType",
    parseAsString,
  );
  const [stateProvince, setStateProvince] = useQueryState(
    "stateProvince",
    parseAsString,
  );
  const [city, setCity] = useQueryState("city", parseAsString);
  const [name, setName] = useQueryState("name", parseAsString);

  return {
    type,
    setType,
    groupInstitutionType,
    setGroupInstitutionType,
    stateProvince,
    setStateProvince,
    city,
    setCity,
    name,
    setName,
  };
};

export const OrganizationsFilters = () => {
  const [open, setOpen] = useState(false);

  const {
    name,
    setName,
    city,
    setCity,
    stateProvince,
    setStateProvince,
    type,
    setType,
    groupInstitutionType,
    setGroupInstitutionType,
  } = useOrganizationsFilters();

  const formRef = useRef<FormRef<typeof schema>>(null);

  const handleApplyFilters = (values: z.infer<typeof schema>) => {
    setName(values.name || null);
    setCity(values.city || null);
    setStateProvince(values.stateProvince || null);
    setType(values.type || null);
    setGroupInstitutionType(values.groupInstitutionType || null);
    setOpen(false);
  };

  const handleClearFilters = () => {
    setName(null);
    setCity(null);
    setStateProvince(null);
    setType(null);
    setGroupInstitutionType(null);
    formRef.current?.formHandler.reset();
    setOpen(false);
  };

  const activeFilterCount = [
    name,
    city,
    stateProvince,
    type,
    groupInstitutionType,
  ].filter(Boolean).length;

  return (
    <Dropdown open={open} onOpenChange={setOpen}>
      <DropdownTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "relative flex items-center gap-2 px-3 py-2",
            "border-gray-300 bg-white text-gray-700 hover:border-gray-400 hover:bg-gray-50",
            "dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:border-gray-500 dark:hover:bg-gray-700",
          )}
        >
          <ListFilter className="h-4 w-4" />
          {activeFilterCount > 0 && (
            <span className="absolute right-0 top-0 flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-xs font-medium text-white">
              {activeFilterCount}
            </span>
          )}
        </Button>
      </DropdownTrigger>

      <DropdownContent className="z-30 rounded-lg bg-white drop-shadow-2xl dark:bg-gray-700">
        <Form
          schema={schema}
          mode="onChange"
          onSubmit={handleApplyFilters}
          className="w-80 sm:w-96"
          ref={formRef}
          defaultValues={{
            name: name || "",
            city: city || "",
            stateProvince: stateProvince || "",
            type: type || "",
            groupInstitutionType: groupInstitutionType || "",
          }}
        >
          <div className="divide-y dark:divide-gray-600">
            <div className="px-4 py-2">
              <Label
                htmlFor="name"
                className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base dark:text-gray-300"
              >
                Name
              </Label>
              <InputField
                name="name"
                id="name"
                placeholder="Search by name..."
              />
            </div>

            <div className="px-4 py-2">
              <Label
                htmlFor="city"
                className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base dark:text-gray-300"
              >
                City
              </Label>
              <InputField
                name="city"
                id="city"
                placeholder="Search by city..."
              />
            </div>

            <div className="px-4 py-2">
              <Label
                htmlFor="stateProvince"
                className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base dark:text-gray-300"
              >
                State/Province
              </Label>
              <SelectProvinces name="stateProvince" />
            </div>

            <div className="px-4 py-2">
              <Label
                htmlFor="type"
                className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base dark:text-gray-300"
              >
                Type
              </Label>
              <Select
                name="type"
                options={TYPE_OPTIONS}
                placeholder="Select type"
              />
            </div>

            <div className="px-4 py-2">
              <Label
                htmlFor="groupInstitutionType"
                className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base dark:text-gray-300"
              >
                Institution Type
              </Label>
              <Select
                name="groupInstitutionType"
                options={GROUP_INSTITUTION_TYPES.map((type) => ({
                  label: capitalize(type),
                  value: type,
                }))}
                placeholder="Select institution type"
              />
            </div>
          </div>

          <div className="flex justify-end gap-5 px-4 py-2">
            <Button
              variant="outline"
              className={cn(
                "w-full justify-center",
                activeFilterCount === 0 && "pointer-events-none invisible",
              )}
              onClick={handleClearFilters}
              type="button"
            >
              Clear Filters
            </Button>
            <Button
              variant="primary"
              type="submit"
              className="w-full justify-center"
            >
              Apply Filters
            </Button>
          </div>
        </Form>
      </DropdownContent>
    </Dropdown>
  );
};
