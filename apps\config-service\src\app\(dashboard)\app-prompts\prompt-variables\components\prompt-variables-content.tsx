"use client";

import { Card } from "flowbite-react";
import { ListFilter } from "lucide-react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useMemo, useState } from "react";
import { HiPlus, HiX } from "react-icons/hi";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";

import { useFilterVariable } from "../hooks/use-filter-variable";
import { usePromptVariables } from "../hooks/use-prompt-variable-queries";
import { generatePromptVariableColumns } from "./columns";
import { FilterModal } from "./filter-variable-modal";
import PromptVariableModal from "./prompt-variable-modal";

export const PromptVariablesContent = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const { key, label, type, computed, published, isActive, isCacheable } =
    useFilterVariable();

  const [isVariableModalOpen, setIsVariableModalOpen] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [selectedVariable, setSelectedVariable] = useState<any>(null);

  const { data, isPending, isPlaceholderData } = usePromptVariables();

  const columns = useMemo(
    () =>
      generatePromptVariableColumns({
        onView: (data) => {
          setSelectedVariable(data);
          setIsVariableModalOpen(true);
        },
      }),
    [],
  );

  const appliedFilters = [
    { label: "Key", value: key, key: "key" },
    { label: "Label", value: label, key: "label" },
    { label: "Type", value: type, key: "type" },
    { label: "Active", value: isActive, key: "isActive" },
    { label: "Published", value: published, key: "published" },
    // { label: "Cacheable", value: isCacheable, key: "isCacheable" },
    { label: "Computed", value: computed, key: "computed" },
  ];

  const countedFilters = appliedFilters.filter(
    (filter) =>
      filter.value !== null &&
      filter.value !== undefined &&
      filter.value !== "",
  );

  const handleRemoveFilter = (key: string) => {
    const currentParams = new URLSearchParams(
      Array.from(searchParams.entries()),
    );
    currentParams.delete(key);
    currentParams.set("page", "1");

    const query = currentParams ? `?${currentParams.toString()}` : "";
    router.push(`${pathname}${query}`);
  };

  const handleCloseVariableModal = () => {
    setIsVariableModalOpen(false);
    setSelectedVariable(null);
  };

  const renderTable = () => {
    if (isPending) return <TableLoading columns={columns} />;
    return (
      <>
        <LoadingWrapper isLoading={isPlaceholderData}>
          <TableData data={data?.results ?? []} columns={columns} />
        </LoadingWrapper>
        {data?.metadata && <TableDataPagination metadata={data.metadata} />}
      </>
    );
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="mb-2 text-3xl font-bold text-gray-900">
          Prompt Variables
        </h1>
        <p className="text-gray-500">
          Manage variables that can be used in prompt templates for dynamic
          content.
        </p>
      </div>

      <Card
        theme={{
          root: {
            children: "p-0",
          },
        }}
      >
        <div className="space-y-2 p-6">
          <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
            <Button
              variant="outline"
              onClick={() => setIsFilterModalOpen(true)}
              className="flex items-center gap-2"
            >
              <ListFilter className="h-4 w-4" />
              Filter
              {countedFilters.length > 0 && (
                <span className="ml-1 flex size-6 items-center justify-center rounded-full bg-blue-600 text-xs font-semibold text-white">
                  {countedFilters.length}
                </span>
              )}
            </Button>
            <Button
              variant="primary"
              onClick={() => setIsVariableModalOpen(true)}
              className="flex items-center gap-2"
            >
              <HiPlus className="h-4 w-4" />
              Add New Variable
            </Button>
          </div>

          <div className="flex max-w-full flex-wrap items-center gap-2 overflow-hidden">
            {appliedFilters.map((filter) => {
              if (!filter.value) return null;
              return (
                <div
                  key={filter.key}
                  className="flex items-center gap-1 rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                >
                  <span className="max-w-[160px] truncate">
                    {filter.label} :{" "}
                    {typeof filter.value === "boolean" ? "Yes" : filter.value}
                  </span>
                  <button
                    onClick={() => handleRemoveFilter(filter.key)}
                    className="ml-1 flex-shrink-0 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    <HiX className="h-4 w-4" />
                  </button>
                </div>
              );
            })}
          </div>
        </div>

        {renderTable()}
      </Card>

      {/* Modals */}
      {isVariableModalOpen && (
        <PromptVariableModal
          isOpen={isVariableModalOpen}
          onClose={handleCloseVariableModal}
          selectedPromptVariable={selectedVariable}
        />
      )}

      {isFilterModalOpen && (
        <FilterModal
          isOpen={isFilterModalOpen}
          onClose={() => setIsFilterModalOpen(false)}
        />
      )}
    </div>
  );
};
