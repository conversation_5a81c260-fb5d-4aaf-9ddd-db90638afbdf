import { DocumentType } from "@/components/icons/doc-icons";

import { OriginationType } from "../enums/originationType.enum";
import { RedactionBox, RedactionMetadata } from "./redaction";
import { UserProfile } from "./user";

export type FileRecord = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  fileType: string;
  extension: DocumentType;
  location: string;
  hmacKey: string;
  hmac: string;
  hmacVerified: boolean;
  originationType: OriginationType;
  originationProfileId: string;
  originationProfile: UserProfile;
  originationDevice: string;
  originationTimeStamp: Date;
  hasCopyPdf: boolean;
  pdfCopyFileId?: string;
  hasRedactedVersion: boolean;
  redactedFileId: string;
  redactionBoxes: RedactionBox[];
  redactionMetadata: RedactionMetadata;
};
