import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { ChangeUserProfileStatusPayload } from "@/lib/apis/users/types";

import { USE_GROUP_PROFILES_QUERY_KEY } from "../../hooks/use-group-profiles";

export const useUpdateProfileStatus = () => {
  return useMutation({
    mutationFn: (payload: ChangeUserProfileStatusPayload) => {
      return api.users.changeProfileStatus(payload);
    },

    onSettled: (_, err, payload) =>
      !err &&
      toast.success(
        `${payload.isActive ? "Enabled" : "Disabled"} profile successfully`,
      ),
    onError: (err) => {
      toast.error(err.message);
    },
    meta: {
      awaits: [USE_GROUP_PROFILES_QUERY_KEY],
    },
  });
};
