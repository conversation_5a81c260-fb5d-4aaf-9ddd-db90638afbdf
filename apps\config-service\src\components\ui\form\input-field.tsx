"use client";
import type { TextInputProps } from "flowbite-react";
import { TextInput } from "flowbite-react";
import { forwardRef } from "react";
import { Controller, useFormContext } from "react-hook-form";
import type { Merge, SetRequired } from "type-fest";

import { cn } from "@/lib/utils";

export type InputFieldProps = Merge<
  SetRequired<TextInputProps, "name">,
  {
    shouldShowError?: boolean;
    positiveNumbersOnly?: boolean;
  }
>;

const InputField: React.ForwardRefExoticComponent<
  InputFieldProps & React.RefAttributes<HTMLInputElement>
> = forwardRef<HTMLInputElement, InputFieldProps>(
  (
    { name, shouldShowError = true, positiveNumbersOnly = false, ...props },
    ref,
  ) => {
    const { control } = useFormContext();

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (positiveNumbersOnly && props.type === "number") {
        const value = parseFloat(e.target.value);
        if (value < 0) {
          e.target.value = "0";
        }
      }
      return e;
    };

    return (
      <Controller
        name={name}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorMessage = errors[name]?.message?.valueOf();
          const hasError = typeof errorMessage === "string";

          return (
            <div>
              <TextInput
                {...field}
                {...props}
                ref={ref}
                onChange={(e) => {
                  const processedEvent = handleChange(e);
                  field.onChange(processedEvent);
                  props.onChange?.(processedEvent);
                }}
                min={
                  positiveNumbersOnly && props.type === "number" ? 0 : undefined
                }
                className={cn(
                  props.className,
                  hasError &&
                    "[&_input]:!border-red-500 [&_input]:!ring-red-500",
                )}
                color={hasError ? "failure" : undefined}
              />
              {hasError && shouldShowError && (
                <span className="text-sm text-red-500">{errorMessage}</span>
              )}
            </div>
          );
        }}
      />
    );
  },
);

InputField.displayName = "InputField";
export { InputField };
