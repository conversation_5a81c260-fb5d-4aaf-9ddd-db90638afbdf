"use client";

import { useAuthentication } from "@/contexts/authentication";
import { useClerkToken } from "@/contexts/clerk-token";
import { useUserTypeStore } from "@/contexts/user-type";

export const clearAllStores = () => {
  // Clear authentication store
  useAuthentication.setState({
    authenticated: false,
    user: undefined,
    error: undefined,
    invalidRole: false,
  });

  // Clear clerk token store
  useClerkToken.setState({
    token: undefined,
    isLoaded: false,
  });

  // Clear user type store
  useUserTypeStore.setState({
    userType: undefined,
  });
};
