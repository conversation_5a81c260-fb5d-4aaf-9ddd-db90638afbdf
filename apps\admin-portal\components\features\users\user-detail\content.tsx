"use client";

import { RotateCcw } from "lucide-react";
import { useParams } from "next/navigation";
import { useState } from "react";
import { CiEdit } from "react-icons/ci";
import { MdBlock } from "react-icons/md";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { ActiveStatusBadge } from "@/components/ui/badges";
import { UserStatusBadge } from "@/components/ui/badges/user-status-badge";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { PageHeader } from "@/components/ui/page-header";
import { Skeleton } from "@/components/ui/skeleton";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";
import { formatDate } from "@/lib/utils";

import { useUpdateUserStatus } from "../hooks/use-users-mutations";
import { useUser } from "../hooks/use-users-queries";
import { ModalEditUser } from "./modal-edit-user";
import { EnrollmentTab, ProfilesSection } from "./tabs";

const USER_TABS = [
  {
    key: "profiles",
    content: <ProfilesSection />,
  },
  {
    key: "enrollments",
    content: <EnrollmentTab />,
  },
];

export const UserDetailContent = () => {
  const [isOpen, setIsOpen] = useState(false);
  const params = useParams();
  const id = params.id as string;
  const { data: user, isPending: isLoadingUser } = useUser(id);
  const { mutateAsync: updateUserStatus, isPending } = useUpdateUserStatus();

  const breadcrumbItems = [
    { label: "Users", href: "/users" },
    { label: user?.firstName ?? "User Detail", loading: isLoadingUser },
  ];

  return (
    <>
      <div className="flex flex-col gap-4 ">
        <Breadcrumb items={breadcrumbItems} />
        <div className="flex items-center justify-between">
          <PageHeader showBackButton href="/users">
            <div className="flex gap-4 ">
              {isLoadingUser ? (
                <Skeleton className="h-7 w-36" />
              ) : (
                `${user?.firstName} ${user?.lastName}`
              )}
            </div>
          </PageHeader>

          {user && (
            <div>
              {user.isActive ? (
                <Button
                  onClick={async () => {
                    await updateUserStatus({
                      id: user.id,
                      isActive: false,
                    });
                  }}
                  isLoading={isPending}
                  disabled={isPending}
                  variant="outline"
                  className="border-red-500 text-red-500 dark:border-red-600 dark:text-red-600"
                >
                  <MdBlock />
                  Disable
                </Button>
              ) : (
                <Button
                  onClick={async () => {
                    await updateUserStatus({
                      id: user.id,
                      isActive: true,
                    });
                  }}
                  isLoading={isPending}
                  disabled={isPending}
                  variant="outline"
                  className="dark:text-green-border-green-700 border-green-500 text-green-500 dark:border-green-700"
                >
                  <RotateCcw className="size-3" />
                  Enable
                </Button>
              )}
            </div>
          )}
        </div>

        {isLoadingUser ? (
          <UserContentSkeleton />
        ) : (
          <OverviewCard
            title="Overview"
            rightContent={
              <Button variant="primary" onClick={() => setIsOpen(true)}>
                <CiEdit />
                Edit User
              </Button>
            }
          >
            <div className="grid grid-cols-2 gap-4 lg:grid-cols-3">
              <OverviewItem
                label="Name"
                value={user ? `${user?.firstName} ${user?.lastName}` : "N/A"}
              />
              <OverviewItem label="Status">
                <ActiveStatusBadge isActive={user?.isActive || false} />
              </OverviewItem>
              <OverviewItem label="Phone" value={user?.phone ?? "N/A"} />

              <OverviewItem
                label="Last Login"
                value={user?.lastLogin ? formatDate(user.lastLogin) : "N/A"}
              />
              <OverviewItem label="Profile Color">
                {user?.userSettings?.profileIcon?.color ? (
                  <div
                    className="size-5 rounded-lg"
                    style={{
                      backgroundColor: user?.userSettings?.profileIcon?.color,
                    }}
                  />
                ) : (
                  "N/A"
                )}
              </OverviewItem>
              <OverviewItem label="Email">
                <span className="break-words text-gray-500">{user?.email}</span>
              </OverviewItem>
            </div>
          </OverviewCard>
        )}
        <TabsWrapper tabs={USER_TABS} />
      </div>
      <ModalEditUser
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        user={user}
      />
    </>
  );
};

const UserContentSkeleton = () => {
  return (
    <div className="flex flex-col">
      <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
        <Skeleton className="mb-3 h-6 w-24" />
        <div className="grid grid-cols-2 gap-4 lg:grid-cols-3">
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="size-5" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-40" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
      </div>
    </div>
  );
};
