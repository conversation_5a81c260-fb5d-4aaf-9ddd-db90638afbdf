import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import { Checkbox, Form, InputField, Select } from "@/components/ui/form";
import { InputNumber } from "@/components/ui/form/input-number";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import { cn } from "@/lib/utils";

import { useFilterPrompt } from "../hooks/use-filter-prompts";
import { useModels } from "../hooks/use-protocol-prompt-query";

const MODEL_PROVIDERS = [
  {
    label: "Gemini",
    value: "gemini",
  },
  {
    label: "OpenAI",
    value: "openai",
  },
] as const;

const PROMPT_TYPES = [
  {
    label: "Protocol Explorer",
    value: "protocolExplorer",
  },
  {
    label: "ISF Artifact Category",
    value: "isfArtifactCategory",
  },
  {
    label: "TMF Artifact Category",
    value: "tmfArtifactCategory",
  },
] as const;

const schema = z.object({
  name: z.string().optional(),
  version: z.string().optional(),
  model: z.string().optional(),
  provider: z.string().optional(),
  key: z.string().optional(),
  isActive: z.boolean().optional(),
});

type FilterFormValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
};

export const FilterModal = ({ isOpen, onClose }: Props) => {
  const {
    active,
    name,
    setActive,
    setName,
    setVersion,
    version,
    key,
    setKey,
    model,
    setModel,
    goToPage,
    modelProvider,
    setModelProvider,
  } = useFilterPrompt();

  const formHandler = useForm<FilterFormValues>({
    mode: "onChange",
    resolver: zodResolver(schema),
    defaultValues: {
      name: name || "",
      version: version || "",
      isActive: active === true ? true : false,
      model: model || "",
      provider: modelProvider || "",
      key: PROMPT_TYPES.some((type) => type.value === key)
        ? (key as string)
        : "",
    },
  });
  const provider = formHandler.watch("provider");

  const { data } = useModels(provider || modelProvider || "");

  const isClearFilterButtonVisible = !!active || !!name || !!version;

  const handleSubmit = (data: FilterFormValues) => {
    setKey(data.key || null);
    setModelProvider(data.provider || null);
    setModel(data.model || null);

    setActive(data.isActive || null);
    setName(data.name || null);
    setVersion(data.version || null);
    goToPage(1);
    onClose();
  };

  const handleClearFilters = () => {
    setKey(null);
    setModelProvider(null);
    setModel(null);
    setActive(null);
    setName(null);
    setVersion(null);
    goToPage(1);

    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Filter Protocol Prompt</Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit} formMethods={formHandler}>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="flex flex-col gap-2 sm:col-span-2">
              <Label htmlFor="name">Name</Label>
              <InputField
                id="name"
                name="name"
                placeholder="Search by name..."
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="version">Version</Label>
              <InputNumber
                id="version"
                name="version"
                placeholder="Search by version..."
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="key">Key</Label>
              <Select
                id="key"
                name="key"
                placeholder="Select a key"
                options={PROMPT_TYPES.map((type) => ({
                  label: type.label,
                  value: type.value,
                }))}
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="provider">Model Provider</Label>
              <Select
                id="provider"
                name="provider"
                placeholder="Select a provider"
                options={MODEL_PROVIDERS.map((type) => ({
                  label: type.label,
                  value: type.value,
                }))}
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="model">Model</Label>
              <Select
                id="model"
                name="model"
                placeholder="Select a model"
                options={
                  data?.results.map((type) => ({
                    label: type,
                    value: type,
                  })) ?? []
                }
              />
            </div>

            <div className="col-span-2 flex flex-wrap items-center gap-x-8 gap-y-2">
              <div className="flex items-center gap-2">
                <Checkbox id="isActive" name="isActive" />
                <Label htmlFor="isActive">Active</Label>
              </div>
            </div>
          </div>

          <div className="flex flex-col justify-end gap-5 border-none pt-4 sm:flex-row">
            <Button
              onClick={handleClearFilters}
              type="button"
              variant="outline"
              className={cn(!isClearFilterButtonVisible && "hidden")}
            >
              Clear Filters
            </Button>
            <Button type="submit" color="blue">
              Apply Filters
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
