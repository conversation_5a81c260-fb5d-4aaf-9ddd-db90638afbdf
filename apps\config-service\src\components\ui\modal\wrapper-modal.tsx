import { Modal as FlowbiteModal } from "flowbite-react";
import { ComponentProps } from "react";

import { Modal } from "../modal";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  title: React.ReactNode;
  children: React.ReactNode;
  className?: string;
} & Omit<ComponentProps<typeof FlowbiteModal>, "title">;

export const WrapperModal = function ({
  isOpen,
  onClose,
  title,
  children,
  className,
  size = "2xl",
  ...rest
}: Props) {
  return (
    <Modal
      show={isOpen}
      onClose={onClose}
      size={size}
      {...rest}
      className={className}
    >
      <Modal.Header className="border-b-0">{title}</Modal.Header>
      <Modal.Body className="!mt-0 !pt-0">{children}</Modal.Body>
    </Modal>
  );
};
