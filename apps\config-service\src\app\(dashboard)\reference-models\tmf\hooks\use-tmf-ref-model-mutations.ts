import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import {
  AddTMFRefModelPayload,
  UpdateTMFRefModelPayload,
} from "@/lib/apis/artifact-categories/types";
import { tmfRefModelApi } from "@/lib/apis/tmf-ref-models";

import { tmfRefModelKeys } from "./use-tmf-ref-model-queries";

export const useAddTMFRefModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: AddTMFRefModelPayload) =>
      tmfRefModelApi.create(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: tmfRefModelKeys.allLists() });
      toast.success("Add TMF Reference Model successfully");
    },
    onError: (err: any) =>
      toast.error(err?.message || "Fail to add TMF Reference Model"),
  });
};

export const useUpdateTMFRefModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateTMFRefModelPayload) =>
      tmfRefModelApi.update(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: tmfRefModelKeys.allLists() });
      toast.success("Update TMF Reference Model successfully");
    },
    onError: (err: any) =>
      toast.error(err?.message || "Fail to update TMF Reference Model"),
  });
};

export const useDeleteTMFRefModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => tmfRefModelApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: tmfRefModelKeys.allLists() });
      toast.success("Delete TMF Reference Model successfully");
    },
    onError: (err: any) =>
      toast.error(err?.message || "Fail to delete TMF Reference Model"),
  });
};
