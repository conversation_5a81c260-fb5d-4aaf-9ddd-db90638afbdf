import { Spinner } from "flowbite-react";
import { ReactNode } from "react";

interface LoadingWrapperProps {
  isLoading: boolean;
  children: ReactNode;
}

const LoadingWrapper = ({ isLoading, children }: LoadingWrapperProps) => {
  return (
    <div className="relative">
      {isLoading && (
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/80">
          <Spinner size="md" />
        </div>
      )}
      <div className={isLoading ? "opacity-50" : ""}>{children}</div>
    </div>
  );
};

export default LoadingWrapper;
