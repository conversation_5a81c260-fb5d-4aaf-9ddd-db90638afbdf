"use client";

import { useAuth } from "@clerk/nextjs";
import { useQuery } from "@tanstack/react-query";

import { useClerkToken } from "@/contexts/clerk-token";
import api from "@/lib/apis";

export const AUTHENTICATED_QUERY_KEY = ["authenticated"];

export const useAuthenticated = () => {
  const { isLoaded, getToken } = useAuth();
  const { setToken, setIsLoaded } = useClerkToken();

  return useQuery({
    queryFn: async () => {
      const token = await getToken();
      if (token) {
        setToken(token);
        setIsLoaded(true);
      }
      return api.authApi.authenticated();
    },
    queryKey: AUTHENTICATED_QUERY_KEY,
    enabled: isLoaded,
    refetchOnWindowFocus: false,
  });
};
