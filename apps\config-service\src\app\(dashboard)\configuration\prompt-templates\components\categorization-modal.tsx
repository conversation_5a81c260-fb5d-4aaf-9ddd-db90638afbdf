import { useMutation } from "@tanstack/react-query";
import { ColumnDef } from "@tanstack/react-table";
import { File as FileIcon } from "lucide-react";
import React, { useState } from "react";
import { z } from "zod";

import { TableData } from "@/components";
import { Button } from "@/components/ui/button";
import { FileDropzone, Form } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";

const CATEGORIZATION_TYPES = ["isf", "tmf"] as const;

const schema = z.object({
  artifactCategoryVersionId: z
    .string({
      invalid_type_error: "Category version is required",
      required_error: "Category version is required",
    })
    .min(1, "Category version is required"),
  files: z.array(z.instanceof(File)).min(1, "At least one file is required"),
  type: z.string().default("isf"),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
};

type CategorizedDocument = {
  name: string;
  category?: {
    isfZoneName?: string;
    isfSectionName?: string;
    isfRecordGroupName?: string;
    tmfZoneName?: string;
    tmfSectionName?: string;
    tmfRecordGroupName?: string;
  };
};

export const CategorizationModal = ({ isOpen, onClose }: Props) => {
  const [categorizedDocument, setCategorizedDocument] = useState<
    CategorizedDocument[]
  >([]);
  const [showResults, setShowResults] = useState(false);

  const { mutateAsync, isPending } = useMutation({
    mutationFn: async (payload: any) => {
      // Mock implementation since this is a config service
      // In real implementation, this would call the categorization API
      return {};
    },
  });

  const onSubmit = async (data: z.infer<typeof schema>) => {
    try {
      // Mock categorization results
      const mockResults = data.files.map((file) => ({
        name: file.name,
        category: {
          isfZoneName: "Mock ISF Zone",
          isfSectionName: "Mock ISF Section",
          isfRecordGroupName: "Mock ISF Record Group",
          tmfZoneName: "Mock TMF Zone",
          tmfSectionName: "Mock TMF Section",
          tmfRecordGroupName: "Mock TMF Record Group",
        },
      }));

      setCategorizedDocument(mockResults);
      setShowResults(true);
    } catch (error) {
      console.error("Categorization failed:", error);
    }
  };

  const handleClose = () => {
    setShowResults(false);
    setCategorizedDocument([]);
    onClose();
  };

  if (showResults) {
    return (
      <Modal show={isOpen} onClose={handleClose} size="7xl">
        <Modal.Header>Categorized Document</Modal.Header>
        <Modal.Body>
          <TableData columns={columns} data={categorizedDocument} />
          <div className="mt-4 flex justify-end">
            <Button onClick={handleClose} variant="outline">
              Close
            </Button>
          </div>
        </Modal.Body>
      </Modal>
    );
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Auto Categorization</Modal.Header>
      <Modal.Body>
        <Form
          defaultValues={{
            artifactCategoryVersionId: "",
            files: [],
            type: "isf",
          }}
          schema={schema}
          onSubmit={onSubmit}
          className="space-y-4"
        >
          <div className="space-y-2">
            <Label htmlFor="files">Files</Label>
            <FileDropzone
              name="files"
              acceptTypes={[
                ".txt",
                ".csv",
                ".xls",
                ".xlsx",
                ".pdf",
                ".doc",
                ".docx",
                ".jpg",
                ".jpeg",
                ".png",
                ".eml",
              ]}
              maxSizeMB={50}
              multiple
            />
          </div>

          <div className="space-y-2">
            <Label>Categorization Type</Label>
            <div className="flex items-center gap-4">
              {CATEGORIZATION_TYPES.map((type) => (
                <div key={type} className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="type"
                    id={type}
                    value={type}
                    defaultChecked={type === "isf"}
                    className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <Label htmlFor={type} className="font-medium uppercase">
                    {type}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="mt-4 flex flex-col justify-end gap-5 border-none pt-4 sm:flex-row">
            <Button onClick={onClose} variant="outline">
              Cancel
            </Button>
            <Button
              type="submit"
              color="blue"
              disabled={isPending}
              isLoading={isPending}
            >
              Save
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

const columns: ColumnDef<CategorizedDocument>[] = [
  {
    header: "Document Name",
    accessorKey: "name",
    cell: ({ row }) => {
      const fileName = row.original?.name;

      if (!fileName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return (
        <div className="flex items-center gap-2">
          <FileIcon className="size-6 flex-shrink-0 text-blue-500" />
          <span className="text-xs font-bold">{fileName}</span>
        </div>
      );
    },
  },
  {
    header: "ISF Zone Name",
    accessorKey: "category.isfZoneName",
    cell: ({ row }) => {
      const zoneName = row.original?.category?.isfZoneName;

      if (!zoneName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return <span className="text-xs font-normal">{zoneName}</span>;
    },
  },
  {
    header: "ISF Section Name",
    accessorKey: "category.isfSectionName",
    cell: ({ row }) => {
      const sectionName = row.original?.category?.isfSectionName;

      if (!sectionName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return <span className="text-xs font-normal">{sectionName}</span>;
    },
  },
  {
    header: "ISF Record Group Name",
    accessorKey: "category.isfRecordGroupName",
    cell: ({ row }) => {
      const artifactName = row.original?.category?.isfRecordGroupName;

      if (!artifactName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return <span className="text-xs font-normal">{artifactName}</span>;
    },
  },
  {
    header: "TMF Zone Name",
    accessorKey: "category.tmfZoneName",
    cell: ({ row }) => {
      const zoneName = row.original?.category?.tmfZoneName;

      if (!zoneName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return <span className="text-xs font-normal">{zoneName}</span>;
    },
  },
  {
    header: "TMF Section Name",
    accessorKey: "category.tmfSectionName",
    cell: ({ row }) => {
      const sectionName = row.original?.category?.tmfSectionName;

      if (!sectionName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return <span className="text-xs font-normal">{sectionName}</span>;
    },
  },
  {
    header: "TMF Record Group Name",
    accessorKey: "category.tmfRecordGroupName",
    cell: ({ row }) => {
      const artifactName = row.original?.category?.tmfRecordGroupName;

      if (!artifactName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return <span className="text-xs font-normal">{artifactName}</span>;
    },
  },
];
