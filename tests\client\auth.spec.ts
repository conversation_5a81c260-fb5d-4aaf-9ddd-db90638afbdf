import test, { expect } from "@playwright/test";

import { USERS } from "../fixtures/auth";
import { login, SIGN_IN_ADMIN_URL, SIGN_IN_CLIENT_URL } from "../utils/auth";

test.describe("Authentication", () => {
  test("Site user can access client portal and sign out", async ({ page }) => {
    await login({
      page,
      user: USERS.site,
    });
    await expect(page).toHaveURL(USERS.site.expectUrl);
    await page.click("button#profile-btn");
    await page.getByRole("button", { name: "Sign Out" }).click();
    await expect(page).toHaveURL(SIGN_IN_CLIENT_URL);
  });

  test("CRO user can access client portal and sign out", async ({ page }) => {
    await login({
      page,
      user: USERS.cro,
    });
    await expect(page).toHaveURL(USERS.cro.expectUrl);
    await page.click("button#profile-btn");
    await page.getByRole("button", { name: "Sign Out" }).click();
    await expect(page).toHaveURL(SIGN_IN_CLIENT_URL);
  });

  test("Wrong email should show error", async ({ page }) => {
    await page.goto(SIGN_IN_CLIENT_URL);
    await page
      .getByPlaceholder("Enter your email address")
      .fill("<EMAIL>");
    await page.getByText("Continue", { exact: true }).click();

    await expect(
      page
        .locator("#error-identifier")
        .or(page.getByText("Couldn't find your account.")),
    ).toBeVisible();
  });

  test("Wrong password should show error-password", async ({ page }) => {
    await page.goto(SIGN_IN_CLIENT_URL);
    await page
      .getByPlaceholder("Enter your email address")
      .fill(USERS.site.email);
    await page.getByText("Continue", { exact: true }).click();

    await expect(page).toHaveURL(`${SIGN_IN_CLIENT_URL}/factor-one`);

    await page.getByPlaceholder("Enter your password").fill("wrong_password");
    await page.getByText("Continue", { exact: true }).click();

    await expect(
      page
        .locator("#error-password")
        .or(page.getByText("Password is incorrect")),
    ).toBeVisible();
  });

  test("Admin user accessing client portal should redirect to admin portal when click on Admin Portal", async ({
    page,
  }) => {
    await login({
      page,
      user: USERS.admin,
      loginUrl: SIGN_IN_CLIENT_URL,
    });

    await expect(
      page.getByRole("heading", { name: "Access Denied" }),
    ).toBeVisible();

    await page.getByRole("button", { name: "Go to Admin Portal" }).click();
    await expect(page).toHaveURL(SIGN_IN_ADMIN_URL);
  });

  test("Admin user accessing client portal should redirect to login page when click on cancel", async ({
    page,
  }) => {
    await login({
      page,
      user: USERS.admin,
      loginUrl: SIGN_IN_CLIENT_URL,
    });

    await expect(
      page.getByRole("heading", { name: "Access Denied" }),
    ).toBeVisible();
    await page.getByRole("button", { name: "Cancel" }).click();
    await expect(page).toHaveURL(SIGN_IN_CLIENT_URL);
  });
});
