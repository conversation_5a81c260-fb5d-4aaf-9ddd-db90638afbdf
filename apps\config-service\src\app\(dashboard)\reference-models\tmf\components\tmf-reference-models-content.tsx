"use client";

import { Card } from "flowbite-react";
import { useState } from "react";
import { HiPlus } from "react-icons/hi";

import { LayoutContent } from "@/components";
import { SearchField } from "@/components/shared/search-field";
import { Button } from "@/components/ui/button";
import { TMFRefModel } from "@/lib/apis/artifact-categories/types";

import { TMFModal } from "./tmf-modal";
import { TMFTable } from "./tmf-table";

export const TMFReferenceModelsContent = () => {
  const [isTMFModalOpen, setIsTMFModalOpen] = useState(false);
  const [selectedTMFModel, setSelectedTMFModel] = useState<TMFRefModel | null>(
    null,
  );

  const handleAddTMFModel = () => {
    setSelectedTMFModel(null);
    setIsTMFModalOpen(true);
  };

  const handleEditTMFModel = (tmfModel: TMFRefModel) => {
    setSelectedTMFModel(tmfModel);
    setIsTMFModalOpen(true);
  };

  const handleCloseTMFModal = () => {
    setIsTMFModalOpen(false);
    setSelectedTMFModel(null);
  };

  return (
    <LayoutContent>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="mb-2 text-3xl font-bold text-gray-900">
            TMF Reference Models
          </h1>
          <p className="text-gray-500">
            Manage TMF (Trial Master File) reference models for document
            classification and organization.
          </p>
        </div>

        <Card
          theme={{
            root: {
              children: "p-0",
            },
          }}
        >
          <div className="">
            <div className="mb-1 flex items-center justify-between p-6">
              <div className="flex w-80 items-center">
                <SearchField placeholder="Search TMF models..." />
              </div>
              <div className="flex items-center gap-4">
                <Button
                  variant="primary"
                  onClick={handleAddTMFModel}
                  className="flex items-center gap-2"
                >
                  <HiPlus className="h-4 w-4" />
                  Add TMF Model
                </Button>
              </div>
            </div>

            <TMFTable onEditTMFModel={handleEditTMFModel} />
          </div>
        </Card>

        {/* Modal */}
        {isTMFModalOpen && (
          <TMFModal
            isOpen
            onClose={handleCloseTMFModal}
            selectedTMFModel={selectedTMFModel}
          />
        )}
      </div>
    </LayoutContent>
  );
};
