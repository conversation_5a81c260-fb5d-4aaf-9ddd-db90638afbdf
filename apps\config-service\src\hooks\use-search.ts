"use client";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

import { useDebounce } from "./use-debounce";

export function useSearch(queryKey = "search") {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const search = searchParams.get(queryKey) ?? undefined;
  const [searchString, setSearchString] = useState(search);
  const searchDebounce = useDebounce(searchString, 500);

  const onSearchChange = useCallback(() => {
    const params = new URLSearchParams(Array.from(searchParams.entries()));
    if (searchDebounce === undefined) return;
    if (searchDebounce.length === 0) {
      params.delete(queryKey);
    } else {
      params.set(queryKey, searchDebounce);
    }
    params.set("page", "1");
    const paramsString = params.toString();
    router.push(`${pathname}?${paramsString}`);
  }, [pathname, router, searchDebounce, searchParams, queryKey]);

  const changeSearch = useCallback(
    (search: string) => {
      setSearchString(() => search);
    },
    [setSearchString],
  );

  useEffect(() => {
    onSearchChange();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchDebounce]);

  return {
    search,
    changeSearch,
  };
}
