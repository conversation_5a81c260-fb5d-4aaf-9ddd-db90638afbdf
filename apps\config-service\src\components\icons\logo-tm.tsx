import type { ComponentProps } from "react";

export const LogoTM = ({ ...props }: ComponentProps<"svg">) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="172"
      height="31"
      viewBox="0 0 172 31"
      {...props}
    >
      <g clipPath="url(#clip0_4632_14549)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.0467 22.4552C6.16266 22.4552 3.01402 19.3413 3.01402 15.5C3.01402 11.6588 6.16266 8.54492 10.0467 8.54492C13.9308 8.54492 17.0794 11.6588 17.0794 15.5H20.0935C20.0935 10.0126 15.5954 5.56415 10.0467 5.56415C4.49808 5.56415 0 10.0126 0 15.5C0 20.9874 4.49808 25.4359 10.0467 25.4359V22.4552Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.0467 22.4552C6.16266 22.4552 3.01402 19.3413 3.01402 15.5C3.01402 11.6588 6.16266 8.54492 10.0467 8.54492C13.9308 8.54492 17.0794 11.6588 17.0794 15.5H20.0935C20.0935 10.0126 15.5954 5.56415 10.0467 5.56415C4.49808 5.56415 0 10.0126 0 15.5C0 20.9874 4.49808 25.4359 10.0467 25.4359V22.4552Z"
        />
        <path d="M17.5818 25.4359C18.9689 25.4359 20.0934 24.3238 20.0934 22.9519C20.0934 21.58 18.9689 20.4679 17.5818 20.4679C16.1946 20.4679 15.0701 21.58 15.0701 22.9519C15.0701 24.3238 16.1946 25.4359 17.5818 25.4359Z" />
        <path d="M40.5519 24.8445C39.1801 24.8445 37.9112 24.6241 36.7454 24.1832C35.5965 23.7253 34.5934 23.0894 33.7362 22.2755C32.8959 21.4447 32.2358 20.4781 31.7558 19.3758C31.2756 18.2736 31.0356 17.0611 31.0356 15.7385C31.0356 14.4158 31.2756 13.2034 31.7558 12.1011C32.2358 10.9989 32.9045 10.0408 33.7619 9.22688C34.6191 8.39598 35.6222 7.76008 36.7711 7.31919C37.9369 6.86134 39.2058 6.63242 40.5776 6.63242C41.9665 6.63242 43.2438 6.86982 44.4099 7.34462C45.593 7.80247 46.596 8.48924 47.419 9.40493L45.7473 11.0074C45.0613 10.2952 44.2897 9.76952 43.4325 9.43036C42.5751 9.07426 41.6579 8.89621 40.6805 8.89621C39.6688 8.89621 38.7258 9.06578 37.8513 9.40493C36.994 9.74408 36.2481 10.2189 35.6137 10.8293C34.9792 11.4398 34.4821 12.169 34.122 13.0168C33.779 13.8477 33.6076 14.755 33.6076 15.7385C33.6076 16.722 33.779 17.6377 34.122 18.4855C34.4821 19.3164 34.9792 20.0371 35.6137 20.6476C36.2481 21.2581 36.994 21.7328 37.8513 22.072C38.7258 22.4112 39.6688 22.5807 40.6805 22.5807C41.6579 22.5807 42.5751 22.4112 43.4325 22.072C44.2897 21.7159 45.0613 21.1732 45.7473 20.4441L47.419 22.0466C46.596 22.9623 45.593 23.6576 44.4099 24.1323C43.2438 24.6071 41.9578 24.8445 40.5519 24.8445ZM51.0881 24.641V6.8359H53.6601V22.4281H63.4078V24.641H51.0881ZM66.3591 24.641V6.8359H68.9311V24.641H66.3591ZM74.3212 24.641V6.8359H76.4302L88.3384 21.4615H87.2324V6.8359H89.8044V24.641H87.6954L75.7872 10.0154H76.8931V24.641H74.3212ZM103.244 24.8445C101.872 24.8445 100.603 24.6241 99.437 24.1832C98.288 23.7253 97.285 23.0894 96.4278 22.2755C95.5875 21.4447 94.9274 20.4781 94.4474 19.3758C93.9672 18.2736 93.7272 17.0611 93.7272 15.7385C93.7272 14.4158 93.9672 13.2034 94.4474 12.1011C94.9274 10.9989 95.5961 10.0408 96.4535 9.22688C97.3107 8.39598 98.3138 7.76008 99.4627 7.31919C100.629 6.86134 101.897 6.63242 103.269 6.63242C104.658 6.63242 105.935 6.86982 107.101 7.34462C108.285 7.80247 109.288 8.48924 110.111 9.40493L108.439 11.0074C107.753 10.2952 106.981 9.76952 106.124 9.43036C105.267 9.07426 104.349 8.89621 103.372 8.89621C102.36 8.89621 101.417 9.06578 100.543 9.40493C99.6856 9.74408 98.9397 10.2189 98.3053 10.8293C97.6708 11.4398 97.1737 12.169 96.8136 13.0168C96.4706 13.8477 96.2992 14.755 96.2992 15.7385C96.2992 16.722 96.4706 17.6377 96.8136 18.4855C97.1737 19.3164 97.6708 20.0371 98.3053 20.6476C98.9397 21.2581 99.6856 21.7328 100.543 22.072C101.417 22.4112 102.36 22.5807 103.372 22.5807C104.349 22.5807 105.267 22.4112 106.124 22.072C106.981 21.7159 107.753 21.1732 108.439 20.4441L110.111 22.0466C109.288 22.9623 108.285 23.6576 107.101 24.1323C105.935 24.6071 104.649 24.8445 103.244 24.8445ZM121.53 24.8445C120.159 24.8445 118.881 24.6156 117.698 24.1577C116.532 23.6999 115.52 23.064 114.663 22.2501C113.806 21.4192 113.137 20.4527 112.657 19.3504C112.177 18.2481 111.937 17.0442 111.937 15.7385C111.937 14.4328 112.177 13.2288 112.657 12.1266C113.137 11.0243 113.806 10.0663 114.663 9.25231C115.52 8.42141 116.532 7.77703 117.698 7.31919C118.864 6.86134 120.142 6.63242 121.53 6.63242C122.902 6.63242 124.162 6.86134 125.311 7.31919C126.477 7.76008 127.489 8.39598 128.346 9.22688C129.221 10.0408 129.889 10.9989 130.352 12.1011C130.832 13.2034 131.072 14.4158 131.072 15.7385C131.072 17.0611 130.832 18.2736 130.352 19.3758C129.889 20.4781 129.221 21.4447 128.346 22.2755C127.489 23.0894 126.477 23.7253 125.311 24.1832C124.162 24.6241 122.902 24.8445 121.53 24.8445ZM121.53 22.5807C122.525 22.5807 123.442 22.4112 124.282 22.072C125.14 21.7328 125.877 21.2581 126.494 20.6476C127.129 20.0202 127.617 19.291 127.96 18.4601C128.32 17.6292 128.5 16.722 128.5 15.7385C128.5 14.755 128.32 13.8477 127.96 13.0168C127.617 12.1859 127.129 11.4652 126.494 10.8548C125.877 10.2274 125.14 9.74408 124.282 9.40493C123.442 9.06578 122.525 8.89621 121.53 8.89621C120.519 8.89621 119.584 9.06578 118.727 9.40493C117.887 9.74408 117.149 10.2274 116.515 10.8548C115.88 11.4652 115.383 12.1859 115.023 13.0168C114.68 13.8477 114.509 14.755 114.509 15.7385C114.509 16.722 114.68 17.6292 115.023 18.4601C115.383 19.291 115.88 20.0202 116.515 20.6476C117.149 21.2581 117.887 21.7328 118.727 22.072C119.584 22.4112 120.519 22.5807 121.53 22.5807ZM139.646 24.641L131.724 6.8359H134.502L141.781 23.2675H140.186L147.516 6.8359H150.088L142.192 24.641H139.646ZM154.725 14.5175H163.984V16.6796H154.725V14.5175ZM154.956 22.4281H165.45V24.641H152.384V6.8359H165.09V9.04883H154.956V22.4281Z" />
        <path d="M167.834 9.14105V7.11889H167.043V6.91541H168.863V7.11889H168.072V9.14105H167.834ZM169.243 9.14105V6.91541H169.439L170.467 8.65141H170.365L171.384 6.91541H171.58V9.14105H171.352V7.26833H171.406L170.467 8.85807H170.355L169.41 7.26833H169.471V9.14105H169.243Z" />
      </g>
      <defs>
        <clipPath id="clip0_4632_14549">
          <rect width="172" height="31" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
