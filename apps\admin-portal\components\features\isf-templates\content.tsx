"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { SearchField } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { PageHeader } from "@/components/ui/page-header";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { IsfTemplate } from "@/lib/apis/isf-templates";

import { generateISFTemplateColumns } from "./columns";
import { useISFTemplates } from "./hooks/use-isf-template-queries";
import { TemplateModal } from "./template-modal";

const BREADCRUMB_ITEMS = [{ label: "ISF Templates" }];

export const ISFTemplateContent = () => {
  const { isOpen, open, close } = useDisclosure();
  const [selectedTemplate, setSelectedTemplate] = useState<IsfTemplate | null>(
    null,
  );
  const { data, isPending, isPlaceholderData } = useISFTemplates();
  const columns = useMemo(
    () =>
      generateISFTemplateColumns({
        onEdit: (template) => {
          setSelectedTemplate(template);
          open();
        },
      }),
    [open],
  );

  const handleCloseModal = () => {
    setSelectedTemplate(null);
    close();
  };

  return (
    <>
      <div className="mb-4 space-y-4">
        <Breadcrumb items={BREADCRUMB_ITEMS} />
        <PageHeader>ISF Template</PageHeader>
      </div>
      <Card className="[&>div]:p-0">
        <div className="flex items-center justify-between p-4">
          <SearchField className="max-w-60" placeholder="Search..." />

          <Button color="blue" onClick={open}>
            <IoMdAdd /> Create Template
          </Button>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} data={data?.results ?? []} />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        )}
      </Card>

      {isOpen && (
        <TemplateModal
          isOpen={isOpen}
          onClose={handleCloseModal}
          template={selectedTemplate}
        />
      )}
    </>
  );
};
