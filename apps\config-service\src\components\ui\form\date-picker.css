/* Date picker custom styles */
.timescape-root {
  display: flex;
  align-items: center;
  gap: 1px;
  border: 1px solid;
  padding: 0.5rem;
}

.timescape-input {
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
  font-size: inherit;
  font-family: inherit;
}

.separator {
  color: #6b7280;
  font-weight: 500;
  user-select: none;
}

/* Custom styles for highlighted dates */
.rdp-root {
  @apply font-poppins;
}

.rdp-day_selected:not(.rdp-day_disabled):not(.rdp-day_outside) {
  background-color: #000 !important;
  color: white !important;
}
.rdp-day_range_middle:not(.rdp-day_disabled):not(.rdp-day_outside) {
  background-color: #f3f4f6 !important;
  color: black !important;
}
.rdp-day_range_start:not(.rdp-day_disabled):not(.rdp-day_outside),
.rdp-day_range_end:not(.rdp-day_disabled):not(.rdp-day_outside) {
  background-color: #000 !important;
  color: white !important;
}
.rdp-button:hover:not([disabled]):not(.rdp-day_selected) {
  background-color: #e5e7eb !important;
}
.rdp-months {
  display: flex !important;
  justify-content: center;
  flex-wrap: nowrap !important;
}
.rdp-month_caption {
  display: flex !important;
  justify-content: center !important;
}
.rdp-nav {
  width: 100% !important;
  display: flex !important;
  justify-content: space-between !important;
}

.rdp-range_end .rdp-day_button {
  background-color: #003 !important;
  border-radius: 8px !important;
  border-style: none !important;
}
.rdp-range_start .rdp-day_button {
  background-color: #003 !important;
  border-radius: 8px !important;
  border-style: none !important;
}

.rdp-today:not(.rdp-outside) {
  color: #003;
  background-color: #e5e7eb;
  border-radius: 8px;
}

.rdp-range_middle {
  background-color: #f3f4f6 !important;
}
.rdp-today.rdp-range_middle {
  background-color: #f3f4f6 !important;
  border-radius: 0px !important;
}

.rdp-day,
.rdp-day_button {
  font-size: 12px !important;
  height: 32px !important;
  width: 32px !important;
}

.rdp-day:hover:not(.rdp-range_middle) {
  background-color: #f3f4f6;
  border-radius: 8px !important;
}

.rdp-button_previous,
.rdp-button_next {
  border-radius: 8px !important;
  border: 1px solid #e5e7eb !important;
  color: #003 !important;
}
.rdp-chevron {
  fill: #003 !important;
  width: 14px !important;
  height: 14px !important;
}

.rdp-caption_label {
  font-size: 14px !important;
  font-weight: 500 !important;
}

.rdp-selected {
  font-weight: 600 !important;
}

/* Style for highlighted dates */
.rdp-day_highlighted:not([data-selected="true"]) {
  @apply !rounded-none bg-purple-50 hover:!rounded-none;
}

/* Style for first date in highlighted range */
.rdp-day_highlighted-first:not([data-selected="true"]) {
  @apply !rounded-l-lg bg-purple-50 hover:!rounded-l-lg;
}

/* Style for last date in highlighted range */
.rdp-day_highlighted-last:not([data-selected="true"]) {
  @apply !rounded-r-lg bg-purple-50 hover:!rounded-r-lg;
}

/* When a date is both first and last (single highlighted date) */
.rdp-day_highlighted-first.rdp-day_highlighted-last:not(
    [data-selected="true"]
  ) {
  @apply !rounded-lg !bg-purple-50 hover:!rounded-lg;
}

/* Ensure highlighted dates that are also selected maintain selected styling */
.rdp-day_highlighted.rdp-day_selected:not(.rdp-day_disabled):not(
    .rdp-day_outside
  ),
.rdp-day_highlighted-first.rdp-day_selected:not(.rdp-day_disabled):not(
    .rdp-day_outside
  ),
.rdp-day_highlighted-last.rdp-day_selected:not(.rdp-day_disabled):not(
    .rdp-day_outside
  ) {
  background-color: #000 !important;
  color: white !important;
}

/* Maintain border radius for first/last highlighted dates when selected */
.rdp-day_highlighted-first.rdp-day_selected:not(.rdp-day_disabled):not(
    .rdp-day_outside
  ) {
  @apply !rounded-l-lg;
}

.rdp-day_highlighted-last.rdp-day_selected:not(.rdp-day_disabled):not(
    .rdp-day_outside
  ) {
  @apply !rounded-r-lg;
}

/* When a date is both first and last (single highlighted date) and selected */
.rdp-day_highlighted-first.rdp-day_highlighted-last.rdp-day_selected:not(
    .rdp-day_disabled
  ):not(.rdp-day_outside) {
  @apply !rounded-lg;
}
