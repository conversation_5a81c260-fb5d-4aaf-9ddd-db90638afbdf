import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const entitlementsKeys = {
  all: () => ["entitlements"] as const,
  allLists: () => [...entitlementsKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...entitlementsKeys.allLists(), params] as const,
};

export const useEntitlements = () => {
  const { page, take } = usePagination();
  const { search } = useSearch();

  const params = {
    page,
    take,
    filter: {
      search,
    },
  };

  return useQuery({
    queryKey: entitlementsKeys.list(params),
    queryFn: () => api.entitlements.list(params),
    placeholderData: (prev) => prev,
  });
};
