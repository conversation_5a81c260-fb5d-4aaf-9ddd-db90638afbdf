import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

import {
  AddCategoryVersionPayload,
  artifactCategories,
  UpdateCategoryVersionPayload,
} from "@/lib/apis/artifact-categories";

import { USE_INFINITE_CATEGORY_VERSIONS_QUERY_KEY } from "./use-infinite-category-versions";

export const useCreateCategoryVersion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: AddCategoryVersionPayload) =>
      artifactCategories.createVersion(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [USE_INFINITE_CATEGORY_VERSIONS_QUERY_KEY],
      });
      toast.success("Category version created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create category version");
    },
  });
};

export const useUpdateCategoryVersion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateCategoryVersionPayload) =>
      artifactCategories.updateVersion(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [USE_INFINITE_CATEGORY_VERSIONS_QUERY_KEY],
      });
      toast.success("Category version updated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update category version");
    },
  });
};

export const useDeleteCategoryVersion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => artifactCategories.deleteVersion(id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [USE_INFINITE_CATEGORY_VERSIONS_QUERY_KEY],
      });
      toast.success("Category version deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete category version");
    },
  });
};
