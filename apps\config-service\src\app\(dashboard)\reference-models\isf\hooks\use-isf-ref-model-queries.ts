import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import { isfRefModelApi } from "@/lib/apis/isf-ref-models";

export const isfRefModelKeys = {
  all: ["isf-ref-models"] as const,
  lists: () => [...isfRefModelKeys.all, "list"] as const,
  list: (params?: any) => [...isfRefModelKeys.lists(), { params }] as const,
  allLists: () => [...isfRefModelKeys.lists()],
};

export const useISFRefModels = () => {
  const { search } = useSearch();
  const { page, take } = usePagination();

  return useQuery({
    queryKey: isfRefModelKeys.list({ search, page, limit: take }),
    queryFn: () =>
      isfRefModelApi.list({
        search,
        page,
        limit: take,
      }),
    placeholderData: (previousData) => previousData,
  });
};
