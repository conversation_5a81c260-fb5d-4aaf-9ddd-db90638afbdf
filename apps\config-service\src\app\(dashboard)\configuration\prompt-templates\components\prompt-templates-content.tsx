"use client";

import { Card } from "flowbite-react";
import { ListFilter } from "lucide-react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useMemo, useState } from "react";
import { HiP<PERSON>, HiX } from "react-icons/hi";

import { LayoutContent } from "@/components";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";
import { Prompt } from "@/lib/apis/prompt-templates";

import { useFilterPrompt } from "../hooks/use-filter-prompts";
import { useActivatePrompt } from "../hooks/use-protocol-prompt-mutations";
import { usePromptTemplates } from "../hooks/use-protocol-prompt-query";
import { CategorizationModal } from "./categorization-modal";
import { generatePromptColumns } from "./columns";
import { ConfirmDeleteModal } from "./confirm-delete-modal";
import { FilterModal } from "./filter-modal";
import PromptModal from "./prompt-modal";

export type ExtendedPrompt = Prompt & {
  isEditing?: boolean;
};

export const PromptTemplatesContent = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const { active, name, version, model, key, modelProvider } =
    useFilterPrompt();

  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [isPromptModalOpen, setIsPromptModalOpen] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [isTestModalOpen, setIsTestModalOpen] = useState(false);

  const [selectedPrompt, setSelectedPrompt] = useState<ExtendedPrompt | null>(
    null,
  );

  const { data, isPending, isPlaceholderData } = usePromptTemplates();
  const { mutate: activatePrompt, isPending: isActivating } =
    useActivatePrompt();

  const handleClosePromptModal = () => {
    setIsPromptModalOpen(false);
    setSelectedPrompt(null);
  };

  const handleCloseConfirmModal = () => {
    setIsConfirmModalOpen(false);
    setSelectedPrompt(null);
  };

  const columns = useMemo(
    () =>
      generatePromptColumns({
        onActivate: (data) => {
          activatePrompt(data.id);
        },
        onDelete: (data) => {
          setSelectedPrompt(data);
          setIsConfirmModalOpen(true);
        },
        onCopy: (data) => {
          setSelectedPrompt(data);
          setIsPromptModalOpen(true);
        },
        onTest: () => {
          setIsTestModalOpen(true);
        },
        onView: (data) => {
          setSelectedPrompt({ ...data, isEditing: true });
          setIsPromptModalOpen(true);
        },
      }),
    [activatePrompt],
  );

  const appliedFilters = [
    { label: "Version", value: version, key: "version" },
    { label: "Model", value: model, key: "model" },
    { label: "Model Provider", value: modelProvider, key: "provider" },
    { label: "Key", value: key, key: "key" },
    { label: "Active", value: active, key: "active" },
    { label: "Name", value: name, key: "name" },
  ];

  const countedFilters = appliedFilters.filter(
    (filter) =>
      filter.value !== null &&
      filter.value !== undefined &&
      filter.value !== "",
  );

  const handleRemoveFilter = (key: string) => {
    const currentParams = new URLSearchParams(
      Array.from(searchParams.entries()),
    );

    currentParams.delete(key);
    currentParams.set("page", "1");
    const query = currentParams ? `?${currentParams.toString()}` : "";
    router.push(`${pathname}${query}`);
  };

  const renderTable = () => {
    if (isPending) return <TableLoading columns={columns} />;
    return (
      <>
        <LoadingWrapper isLoading={isPlaceholderData || isActivating}>
          <TableData data={data?.results ?? []} columns={columns} />
        </LoadingWrapper>
        {data?.metadata && <TableDataPagination metadata={data.metadata} />}
      </>
    );
  };

  return (
    <LayoutContent>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="mb-2 text-3xl font-bold text-gray-900">
            Prompt Templates
          </h1>
          <p className="text-gray-500">
            Manage AI prompt templates for various use cases and configurations.
          </p>
        </div>

        <Card
          theme={{
            root: {
              children: "p-0",
            },
          }}
        >
          <div className="space-y-2 p-6">
            <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
              <Button
                variant="outline"
                onClick={() => setIsFilterModalOpen(true)}
                className="flex items-center gap-2"
              >
                <ListFilter className="h-4 w-4" />
                Filter
                {countedFilters.length > 0 && (
                  <span className="ml-1 flex size-6 items-center justify-center rounded-full bg-blue-600 text-xs font-semibold text-white">
                    {countedFilters.length}
                  </span>
                )}
              </Button>
              <Button
                variant="primary"
                onClick={() => setIsPromptModalOpen(true)}
                className="flex items-center gap-2"
              >
                <HiPlus className="h-4 w-4" />
                Add New Template
              </Button>
            </div>

            <div className="flex max-w-full flex-wrap items-center gap-2 overflow-hidden">
              {appliedFilters.map((filter) => {
                if (!filter.value) return null;
                return (
                  <div
                    key={filter.key}
                    className="flex items-center gap-1 rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                  >
                    <span className="max-w-[160px] truncate">
                      {filter.label} :{" "}
                      {typeof filter.value === "boolean" ? "Yes" : filter.value}
                    </span>
                    <button
                      onClick={() => handleRemoveFilter(filter.key)}
                      className="ml-1 flex-shrink-0 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
                    >
                      <HiX className="h-4 w-4" />
                    </button>
                  </div>
                );
              })}
            </div>
          </div>

          {renderTable()}
        </Card>

        {/* Modals */}
        {isFilterModalOpen && (
          <FilterModal
            isOpen={isFilterModalOpen}
            onClose={() => setIsFilterModalOpen(false)}
          />
        )}

        {isPromptModalOpen && (
          <PromptModal
            selectedPrompt={selectedPrompt}
            isOpen={isPromptModalOpen}
            onClose={handleClosePromptModal}
          />
        )}

        {isConfirmModalOpen && (
          <ConfirmDeleteModal
            onClose={handleCloseConfirmModal}
            isOpen={isConfirmModalOpen}
            selectedPrompt={selectedPrompt as ExtendedPrompt}
          />
        )}

        {isTestModalOpen && (
          <CategorizationModal
            isOpen={isTestModalOpen}
            onClose={() => setIsTestModalOpen(false)}
          />
        )}
      </div>
    </LayoutContent>
  );
};
