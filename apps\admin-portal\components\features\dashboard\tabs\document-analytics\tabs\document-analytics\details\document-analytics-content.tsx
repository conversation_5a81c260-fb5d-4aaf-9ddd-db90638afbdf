"use client";

import { parseAsString, useQueryState } from "nuqs";
import { Fi<PERSON>lock, FiFileText } from "react-icons/fi";

import { Breadcrumb } from "@/components/ui/breadcrumb";
import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { PageHeader } from "@/components/ui/page-header";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { DocumentAnalyticsTable } from "./document-analytics-table";

const DOCUMENT_ANALYTICS_TABS = [
  {
    key: "all",
    title: (
      <p className="flex items-center gap-3">
        <FiFileText className="h-5 w-5 text-blue-500" />
        All Documents
      </p>
    ),
    content: <DocumentAnalyticsTable type="all" />,
  },
  {
    key: "pending",
    title: (
      <p className="flex items-center gap-3">
        <FiClock className="h-5 w-5 text-orange-500" />
        Pending Documents
      </p>
    ),
    content: <DocumentAnalyticsTable type="pending" />,
  },
] as const;

const SELECT_OPTIONS = DOCUMENT_ANALYTICS_TABS.map((tab) => ({
  label: tab.title,
  value: tab.key,
}));

const BREADCRUMB_ITEMS = [{ label: "Document Analytics" }];

export const DocumentAnalyticsContent = () => {
  const [currentTab, setCurrentTab] = useQueryState("tab", parseAsString);

  return (
    <div className="space-y-4">
      <Breadcrumb items={BREADCRUMB_ITEMS} />
      <PageHeader showBackButton href="/dashboards?tab=analytics">
        Document Analytics & Engagement
      </PageHeader>

      <div className="space-y-6">
        {/* Desktop Tabs */}
        <div className="hidden sm:block">
          <TabsWrapper tabs={DOCUMENT_ANALYTICS_TABS} />
        </div>

        {/* Mobile Select */}
        <div className="sm:hidden">
          <UncontrolledSelect
            options={SELECT_OPTIONS}
            value={currentTab ?? "all"}
            onChange={(value) => {
              if (value) {
                setCurrentTab(value);
              }
            }}
            placeholder="Select view"
          />

          <div className="mt-4">
            {DOCUMENT_ANALYTICS_TABS.find((tab) => tab.key === currentTab)
              ?.content ?? DOCUMENT_ANALYTICS_TABS[0].content}
          </div>
        </div>
      </div>
    </div>
  );
};
