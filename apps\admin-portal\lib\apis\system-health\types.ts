import type { ListBaseResponse } from "../types";

export type SystemProcessingErrorsResponse = {
  total: number;
  protocolContentErrors: number;
  sourceDocumentContentErrors: number;
  emailProcessingErrors: number;
  emailToSourceErrors: number;
  emailToISFErrors: number;
  emailToDocExchangeErrors: number;
};

export type HmacVerificationFailuresResponse = {
  total: number;
  sourceDocumentFailures: number;
  artifactVersionFailures: number;
};

export type OfflineScannersResponse = {
  total: number;
};

export type DatabaseTableType =
  | "patients"
  | "source_documents"
  | "activity_log"
  | "artifact_files";

export type DatabaseGrowthResponse = {
  tables: Array<{
    table: DatabaseTableType;
    data: Array<{
      date: string;
      count: number;
    }>;
  }>;
  unit: "day" | "week";
  fromDate: string;
  toDate: string;
  combine: boolean;
};

export type ProcessingErrorSource =
  | "protocol_content"
  | "source_document_content"
  | "email_to_source_documents"
  | "email_to_isf"
  | "email_to_doc_exchange";

export type ProcessingErrorStatus = "pending" | "error" | "resolved";

export type ProcessingErrorRecord = {
  id: string;
  timestamp: string;
  source: ProcessingErrorSource;
  message: string;
  details: Record<string, any>;
  relatedType: string;
  relatedId: string;
  relatedName: string;
  studyId: string | null;
  studyName: string | null;
  patientId: string | null;
  patientName: string | null;
  status: ProcessingErrorStatus;
  type: string;
};

export type ProcessingErrorsListResponse =
  ListBaseResponse<ProcessingErrorRecord>;

export type HmacFailureSource = "source_document" | "artifact_version";

export type HmacFailureRecord = {
  id: string;
  timestamp: string;
  source: HmacFailureSource;
  message: string;
  relatedType: string;
  relatedId: string;
  relatedName: string;
  studyId: string | null;
  siteId: string | null;
  patientId: string | null;
  patientName: string | null;
  isfFolderId: string | null;
  tmfFolderId: string | null;
};

export type HmacFailuresListResponse = ListBaseResponse<HmacFailureRecord>;
