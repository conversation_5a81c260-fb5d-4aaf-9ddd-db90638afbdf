"use client";

import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  offset,
  Placement,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
  useRole,
} from "@floating-ui/react";
import type { ReactNode } from "react";
import { createContext, useContext, useState } from "react";

import { cn } from "@/lib/utils";

/** ======================= DROPDOWN CONTEXT ======================= */
type DropdownContextType = {
  open: boolean;
  setOpen: (open: boolean) => void;
  triggerRef: (element: HTMLElement | null) => void;
  getFloatingProps: (props?: object) => object;
  getReferenceProps: (props?: object) => object;
  floatingStyles: React.CSSProperties;
  setFloating: (element: HTMLElement | null) => void;
  context: any;
};

const DropdownContext = createContext<DropdownContextType | null>(null);

export function useDropdown() {
  const context = useContext(DropdownContext);
  if (!context) {
    throw new Error("useDropdown must be used within a Dropdown");
  }
  return context;
}

/** ======================= DROPDOWN ======================= */
type DropdownProps = {
  children: ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  className?: string;
  placement?: Placement;
};

export function Dropdown({
  children,
  open: controlledOpen,
  onOpenChange,
  className,
  placement,
}: DropdownProps) {
  const [uncontrolledOpen, setUncontrolledOpen] = useState(false);

  // Support both controlled and uncontrolled modes
  const open = controlledOpen !== undefined ? controlledOpen : uncontrolledOpen;
  const setOpen = onOpenChange || setUncontrolledOpen;

  const { refs, floatingStyles, context } = useFloating({
    open,
    onOpenChange: setOpen,
    placement: placement || "bottom-start",
    middleware: [
      offset(4),
      flip({
        fallbackPlacements: ["top-start"],
        padding: 8,
      }),
      shift({
        padding: 8,
      }),
      size({
        apply({ availableWidth, availableHeight, elements }) {
          Object.assign(elements.floating.style, {
            maxWidth: `${Math.min(availableWidth, 90)}px`,
            maxHeight: `${Math.min(availableHeight, 400)}px`,
          });
        },
        padding: 8,
      }),
    ],
    whileElementsMounted: autoUpdate,
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    useClick(context),
    useRole(context, { role: "menu" }),
    useDismiss(context),
  ]);

  const contextValue = {
    open,
    setOpen,
    triggerRef: refs.setReference,
    getFloatingProps,
    getReferenceProps,
    floatingStyles,
    setFloating: refs.setFloating,
    context,
  };

  return (
    <DropdownContext.Provider value={contextValue}>
      <div className={cn("relative", className)}>{children}</div>
    </DropdownContext.Provider>
  );
}

/** ======================= DROPDOWN TRIGGER ======================= */
type DropdownTriggerProps = {
  children: ReactNode;
  className?: string;
  asChild?: boolean;
};

export function DropdownTrigger({ children, className }: DropdownTriggerProps) {
  const { triggerRef, getReferenceProps } = useDropdown();

  return (
    <div
      ref={triggerRef}
      {...getReferenceProps()}
      className={cn("inline-flex min-h-fit", className)}
    >
      {children}
    </div>
  );
}

/** ======================= DROPDOWN CONTENT ======================= */
type DropdownContentProps = {
  children: ReactNode;
  className?: string;
};

export function DropdownContent({ children, className }: DropdownContentProps) {
  const { open, setFloating, getFloatingProps, floatingStyles, context } =
    useDropdown();

  if (!open) return null;

  return (
    <FloatingPortal>
      <FloatingFocusManager context={context} modal={false}>
        <div
          ref={setFloating}
          style={floatingStyles}
          {...getFloatingProps()}
          className={cn(
            "motion-opacity-in-0 motion-scale-in-98 motion-duration-150 motion-ease-out z-50 min-h-fit min-w-fit overflow-hidden rounded-md bg-white shadow-xl",
            className,
          )}
        >
          {children}
        </div>
      </FloatingFocusManager>
    </FloatingPortal>
  );
}

/** ======================= DROPDOWN ITEM ======================= */
type DropdownItemProps = {
  children: ReactNode;
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
};

export function DropdownItem({
  children,
  className,
  onClick,
  disabled = false,
}: DropdownItemProps) {
  const { setOpen } = useDropdown();

  const handleClick = () => {
    if (disabled) return;
    onClick?.();
    setOpen(false);
  };

  return (
    <button
      type="button"
      onClick={handleClick}
      disabled={disabled}
      className={cn(
        "flex w-full cursor-pointer select-none items-center rounded-sm px-[14px] py-2 text-base leading-6 outline-none",
        // "hover:bg-purple-500 hover:text-white",
        disabled && "pointer-events-none opacity-50",
        className,
      )}
      role="menuitem"
    >
      {children}
    </button>
  );
}

/** ======================= DROPDOWN SEPARATOR ======================= */
type DropdownSeparatorProps = {
  className?: string;
};

export function DropdownSeparator({ className }: DropdownSeparatorProps) {
  return <div className={cn("h-px bg-gray-200", className)} role="separator" />;
}

/** ======================= DROPDOWN LABEL ======================= */
type DropdownLabelProps = {
  children: ReactNode;
  className?: string;
};

export function DropdownLabel({ children, className }: DropdownLabelProps) {
  return (
    <div
      className={cn(
        "px-2 py-1.5 text-sm font-semibold text-gray-900",
        className,
      )}
    >
      {children}
    </div>
  );
}
