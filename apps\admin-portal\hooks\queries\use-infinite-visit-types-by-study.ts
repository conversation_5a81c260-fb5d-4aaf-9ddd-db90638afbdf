import { useInfiniteQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";

import api from "@/lib/apis";

export const useInfiniteVisitTypesByStudy = (
  search: string,
  initialPageSize = 10,
) => {
  const studyId = useParams().id as string;

  return useInfiniteQuery({
    queryKey: ["infinite-visit-types-by-study", search, studyId],
    queryFn: ({ pageParam = 1 }) => {
      return api.visitTypes.list({
        page: pageParam,
        take: initialPageSize,
        filter: {
          name: search,
          studyId,
          isActive: true,
        },
      });
    },
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
