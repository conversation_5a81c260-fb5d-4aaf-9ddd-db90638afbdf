"use client";
import { type PropsWithChildren, useLayoutEffect } from "react";

import { useInactivity } from "@/hooks/use-inactive";

import { Header } from "./header";
import { Sidebar } from "./sidebar";

export function DashboardWrapper({ children }: PropsWithChildren) {
  useInactivity();

  useLayoutEffect(() => {
    document.documentElement.classList.remove("dark");
  }, []);
  return (
    <div className="grid h-dvh grid-rows-[auto_1fr] overflow-hidden">
      <Header />
      <div className="grid grid-flow-col grid-cols-[auto_1fr] overflow-y-auto">
        <Sidebar />
        <div className="overflow-y-auto p-4 sm:p-6">{children}</div>
      </div>
    </div>
  );
}
