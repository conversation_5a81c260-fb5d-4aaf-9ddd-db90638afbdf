export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  permissions?: string[];
}

export interface AuthenticatedUser extends User {
  token: string;
}

export interface LoginPayload extends Record<string, unknown> {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
}

export interface ResetPasswordPayload extends Record<string, unknown> {
  token: string;
  password: string;
}

export interface UpdatePasswordPayload extends Record<string, unknown> {
  currentPassword: string;
  newPassword: string;
}
