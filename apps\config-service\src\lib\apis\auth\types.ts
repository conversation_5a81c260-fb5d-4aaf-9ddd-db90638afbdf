export interface User {
  authenticated: boolean;
  userId: string;
  firstName: null | string;
  lastName: null | string;
  email: string;
  publicMetadata: {
    roleId: string;
    groupId: string;
    groupType: string;
  };
}

export interface AuthenticatedUser extends User {
  token: string;
}

export interface LoginPayload extends Record<string, unknown> {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
}

export interface ResetPasswordPayload extends Record<string, unknown> {
  token: string;
  password: string;
}

export interface UpdatePasswordPayload extends Record<string, unknown> {
  currentPassword: string;
  newPassword: string;
}
