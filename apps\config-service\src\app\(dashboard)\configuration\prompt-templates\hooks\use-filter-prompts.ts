import { parseAsBoolean, parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";

export const useFilterPrompt = () => {
  const [active, setActive] = useQueryState("active", parseAsBoolean);
  const [name, setName] = useQueryState("name", parseAsString);
  const [version, setVersion] = useQueryState("version", parseAsString);
  const [key, setKey] = useQueryState("key", parseAsString);
  const [model, setModel] = useQueryState("model", parseAsString);
  const [modelProvider, setModelProvider] = useQueryState(
    "provider",
    parseAsString,
  );
  const { page, take, goToPage } = usePagination();

  return {
    active,
    setActive,
    name,
    setName,
    version,
    setVersion,
    page,
    take,
    goToPage,
    key,
    setKey,
    model,
    setModel,
    modelProvider,
    setModelProvider,
  };
};
