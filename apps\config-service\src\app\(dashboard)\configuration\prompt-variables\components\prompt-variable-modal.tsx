import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z, ZodIssueCode } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import {
  Checkbox,
  Form,
  InputField,
  Select,
  Textarea,
} from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { MonacoEditor } from "@/components/ui/form/monaco-editor";
import { WrapperModal } from "@/components/ui/modal";
import { PromptVariable } from "@/lib/apis/prompt-variables";
import { capitalize } from "@/lib/utils/string";

import {
  useAddPromptVariable,
  useUpdatePromptVariable,
} from "../hooks/use-prompt-variable-mutations";
import { useResolvers } from "../hooks/use-prompt-variable-queries";

export const VARIABLE_TYPES = [
  "string",
  "json",
  "array",
  "number",
  "boolean",
] as const;

const baseSchema = z.object({
  key: z
    .string({
      required_error: "Key is required",
      invalid_type_error: "Key is required",
    })
    .min(1, "Key is required"),
  label: z.string().optional(),
  resolverFunction: z.string().optional(),
  description: z.string().optional(),
  type: z.enum(VARIABLE_TYPES, {
    errorMap: () => ({
      message: "Type is required",
    }),
  }),
  computed: z.boolean().default(false),
  published: z.boolean().default(false),
  isActive: z.boolean().default(true),
  fallbackValue: z.string().optional(),
});

const schema = z.preprocess((input, ctx) => {
  const resolverFunctionFields = baseSchema
    .pick({
      computed: true,
      resolverFunction: true,
      type: true,
    })
    .safeParse(input);

  if (resolverFunctionFields.success) {
    if (
      resolverFunctionFields.data.computed &&
      !resolverFunctionFields.data.resolverFunction
    ) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "Resolver function is required",
        path: ["resolverFunction"],
      });
    }

    if (
      !resolverFunctionFields.data.computed &&
      resolverFunctionFields.data.resolverFunction
    ) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "Computed is required",
        path: ["computed"],
      });
    }

    if (
      resolverFunctionFields.data.computed &&
      resolverFunctionFields.data.type !== "string"
    ) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "Type must be string",
        path: ["type"],
      });
    }
  }
  const typeFields = baseSchema
    .pick({
      type: true,
      fallbackValue: true,
    })
    .safeParse(input);

  if (typeFields.success) {
    const selectedType = typeFields.data.type;
    const fallbackValue = typeFields.data.fallbackValue?.trim();
    if (!fallbackValue || !selectedType) return input;
    if (selectedType === "string" && typeof fallbackValue !== "string") {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "Default value must be a string",
        path: ["fallbackValue"],
      });
    }
    if (selectedType === "number" && isNaN(Number(fallbackValue))) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "Default value must be a number",
        path: ["fallbackValue"],
      });
    }
    if (selectedType === "boolean") {
      try {
        const parsedFallbackValue = JSON.parse(fallbackValue);
        if (typeof parsedFallbackValue !== "boolean") {
          ctx.addIssue({
            code: ZodIssueCode.custom,
            message: "Default value must be a boolean",
            path: ["fallbackValue"],
          });
        }
      } catch {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "Default value must be a boolean",
          path: ["fallbackValue"],
        });
      }
    }
    if (selectedType === "array" || selectedType === "json") {
      try {
        const parsedFallbackValue = JSON.parse(fallbackValue);
        if (selectedType === "array" && !Array.isArray(parsedFallbackValue)) {
          ctx.addIssue({
            code: ZodIssueCode.custom,
            message: "Default value must be an array",
            path: ["fallbackValue"],
          });
        }
        if (
          selectedType === "json" &&
          (typeof parsedFallbackValue !== "object" ||
            parsedFallbackValue === null ||
            Array.isArray(parsedFallbackValue))
        ) {
          ctx.addIssue({
            code: ZodIssueCode.custom,
            message: "Default value must be a json",
            path: ["fallbackValue"],
          });
        }
      } catch {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message:
            selectedType === "array"
              ? "Default value must be an array"
              : "Default value must be a json",
          path: ["fallbackValue"],
        });
      }
    }
  }

  return input;
}, baseSchema);

type FormValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedPromptVariable: PromptVariable | null;
};

const PromptVariableModal = ({
  isOpen,
  onClose,
  selectedPromptVariable,
}: Props) => {
  const { mutateAsync: addVariable, isPending: isAdding } =
    useAddPromptVariable();

  const { mutateAsync: updateVariable, isPending: isUpdating } =
    useUpdatePromptVariable();

  const { data } = useResolvers();
  const [language, setLanguage] = useState<"html" | "json">(
    selectedPromptVariable?.type === "string" ? "html" : "json",
  );

  const defaultFallback = selectedPromptVariable?.fallbackValue
    ? typeof selectedPromptVariable?.fallbackValue === "string"
      ? selectedPromptVariable?.fallbackValue
      : JSON.stringify(selectedPromptVariable?.fallbackValue)
    : "";

  const forms = useForm<FormValues>({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      key: selectedPromptVariable?.key || "",
      label: selectedPromptVariable?.label || "",
      resolverFunction: selectedPromptVariable?.resolverFunction || "",
      description: selectedPromptVariable?.description || "",
      type: selectedPromptVariable?.type,
      computed: selectedPromptVariable?.computed || false,
      published: selectedPromptVariable?.published || false,
      isActive:
        typeof selectedPromptVariable?.isActive === "boolean"
          ? selectedPromptVariable.isActive
          : true,
      fallbackValue: defaultFallback,
    },
  });

  const isEditing = !!selectedPromptVariable;

  const handleSubmit = async (data: FormValues) => {
    try {
      const fallback = data.fallbackValue
        ? data.type === "string"
          ? data.fallbackValue?.trim()
          : JSON.parse(data.fallbackValue?.trim())
        : undefined;
      isEditing
        ? await updateVariable({
            ...data,
            id: selectedPromptVariable.id,
            resolverFunction: data.resolverFunction || undefined,
            fallbackValue: fallback,
          })
        : await addVariable({
            ...data,
            resolverFunction: data.resolverFunction || undefined,
            fallbackValue: fallback,
          });
      onClose();
    } catch {
      console.error("Fail to parse fallback value");
    }
  };

  const handleSelectComputed = () => {
    setTimeout(() => forms.trigger("resolverFunction"));
  };

  useEffect(() => {
    const { unsubscribe } = forms.watch((values, { name }) => {
      if (name === "type" && values.type === "string") {
        setLanguage("html");
      }

      if (name === "type" && values.type !== "string") {
        setLanguage("json");
      }

      if (name !== "resolverFunction") return;
      if (values.resolverFunction) {
        forms.setValue("computed", true);
        forms.setValue("type", "string", {
          shouldValidate: true,
        });
      } else {
        forms.clearErrors("type");
        forms.setValue("computed", false);
      }
    });
    return () => unsubscribe();
  }, []);

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      className="[&>div]:max-w-4xl"
      title={`${isEditing ? "Update" : "New"} Variable`}
    >
      <Form
        className="grid  gap-4 sm:grid-cols-2"
        onSubmit={handleSubmit}
        schema={schema}
        formMethods={forms}
      >
        <div className="space-y-1">
          <Label htmlFor="key">Key</Label>
          <InputField id="key" name="key" placeholder="Enter key" />
        </div>

        <div className="space-y-1">
          <Label htmlFor="type">Type</Label>
          <Select
            id="type"
            name="type"
            placeholder="Select a type"
            onChange={() => {
              setTimeout(() => forms.trigger("fallbackValue"));
            }}
            options={VARIABLE_TYPES.map((type) => ({
              label: capitalize(type),
              value: type,
            }))}
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="label">Label</Label>
          <InputField id="label" name="label" placeholder="Enter label" />
        </div>

        <div className="space-y-1">
          <Label htmlFor="resolverFunction">Variable Resolver Function</Label>
          <Select
            id="resolverFunction"
            name="resolverFunction"
            placeholder="Select a function"
            options={
              data?.results.map((resolver) => ({
                label: capitalize(resolver),
                value: resolver,
              })) ?? []
            }
          />
        </div>

        <div className="space-y-1 sm:col-span-2">
          <Label htmlFor="fallbackValue">Default Value</Label>

          <MonacoEditor
            name="fallbackValue"
            language={language}
            isAllowFormat={true}
            options={{
              wordBasedSuggestions: "off",
              quickSuggestions: false,
            }}
            height={240}
          />
        </div>

        <div className="space-y-1 sm:col-span-2">
          <Label htmlFor="description">Description</Label>
          <Textarea name="description" placeholder="Enter description" />
        </div>

        <div className="sm:col-span-2">
          <div className="flex flex-wrap items-center gap-4  sm:gap-x-6">
            <div className="flex items-center gap-2">
              <Label onClick={handleSelectComputed} htmlFor="computed">
                Computed
              </Label>
              <Checkbox
                onClick={handleSelectComputed}
                id="computed"
                name="computed"
              />
            </div>
            <div className="flex items-center gap-2">
              <Label htmlFor="published">Published</Label>
              <Checkbox id="published" name="published" />
            </div>

            {isEditing && (
              <div className="flex items-center gap-2">
                <Label htmlFor="isActive">Active</Label>
                <Checkbox id="isActive" name="isActive" />
              </div>
            )}
          </div>
          <span className="text-sm text-red-500 dark:text-red-400">
            {forms.formState.errors.computed?.message}
          </span>
        </div>

        <div className="flex flex-col justify-end gap-4 sm:col-span-2 sm:flex-row">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            isLoading={isAdding || isUpdating}
            variant="primary"
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};

export default PromptVariableModal;
