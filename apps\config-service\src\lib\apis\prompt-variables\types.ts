import { VARIABLE_TYPES } from "@/app/(dashboard)/configuration/prompt-variables/components/prompt-variable-modal";

import type { ListBaseResponse } from "../types";

export type ResolverListResponse = {
  results: string[];
};
export type VariableType = (typeof VARIABLE_TYPES)[number];

export type PromptVariable = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  key: string;
  label: string;
  description: string | null;
  type: VariableType;
  computed: boolean;
  fallbackValue: any;
  published: boolean;
  isActive: boolean;
  resolverFunction: string | null;
  isCacheable?: boolean;
};

export type AddPromptVariablePayload = {
  key: string;
  label?: string;
  description?: string;
  type: string;
  computed: boolean;
  fallbackValue?: any;
  published: boolean;
  isActive: boolean;
  resolverFunction?: string;
  isCacheable?: boolean;
};

export type UpdatePromptVariablePayload = AddPromptVariablePayload & {
  id: string;
};

export type PromptVariableListResponse = ListBaseResponse<PromptVariable>;
