"use client";

import { z } from "zod";

import {
  <PERSON><PERSON>,
  CloseButton,
  Modal,
  Modal<PERSON>ody,
  ModalHeader,
} from "@/components";
import {
  DatePicker,
  Form,
  InputField,
  InputNumber,
  Label,
  Textarea,
} from "@/components/ui/form";
import { CategoryVersion } from "@/lib/apis/artifact-categories";

import {
  useCreateCategoryVersion,
  useUpdateCategoryVersion,
} from "../hooks/use-category-version-mutations";

const schema = z.object({
  version: z.number().min(1, "Version must be at least 1"),
  effectiveDate: z
    .string({
      required_error: "Effective date is required",
      invalid_type_error: "Effective date is required",
    })
    .min(1, "Effective date is required"),
  notes: z.string().optional(),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedVersion: CategoryVersion | null;
};

export const CategoryVersionModal = function ({
  isOpen,
  onClose,
  selectedVersion,
}: Props) {
  const { mutateAsync: addCategoryVersion, isPending: isAdding } =
    useCreateCategoryVersion();
  const { mutateAsync: updateCategoryVersion, isPending: isUpdating } =
    useUpdateCategoryVersion();

  const isEditing = !!selectedVersion;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing
      ? await updateCategoryVersion({
          ...data,
          id: selectedVersion.id,
        })
      : await addCategoryVersion(data);

    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="xl">
      <ModalHeader>
        {`${isEditing ? "Edit" : "Add"} Category Version`}
      </ModalHeader>

      <ModalBody className="my-0 !pt-0">
        <Form
          defaultValues={{
            version: selectedVersion?.version || 1,
            notes: selectedVersion?.notes || "",
            effectiveDate: selectedVersion?.effectiveDate || "",
          }}
          mode="onChange"
          schema={schema}
          onSubmit={onSubmit}
        >
          <div className="grid grid-cols-1 gap-6">
            <div className="flex flex-col gap-2">
              <Label htmlFor="version">Version</Label>
              <InputField
                id="version"
                name="version"
                placeholder="Enter version..."
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="effectiveDate">Effective Date</Label>
              <DatePicker id="effectiveDate" name="effectiveDate" />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="notes">Description</Label>
              <Textarea name="notes" placeholder="Enter description..." />
            </div>
          </div>

          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              isLoading={isAdding || isUpdating}
              variant="primary"
            >
              Save
            </Button>
          </div>
        </Form>
      </ModalBody>
    </Modal>
  );
};
