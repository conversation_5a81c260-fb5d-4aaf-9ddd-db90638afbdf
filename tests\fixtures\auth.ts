import { expect, Page, test as base } from "@playwright/test";

import { login } from "../utils/auth";

export const USERS = {
  site: {
    email: process.env.SITE_EMAIL || "<EMAIL>",
    password: process.env.SITE_PASSWORD || "site-password",
    role: "site",
    expectUrl: "https://dev-app.us.clincove.com/sit",
  },
  cro: {
    email: process.env.CRO_EMAIL || "<EMAIL>",
    password: process.env.CRO_PASSWORD || "cro-password",
    role: "cro",
    expectUrl: "https://dev-app.us.clincove.com/cro",
  },
  admin: {
    email: process.env.ADMIN_EMAIL || "<EMAIL>",
    password: process.env.ADMIN_PASSWORD || "admin-password",
    role: "admin",
    expectUrl: "https://dev-admin.us.clincove.com/dashboards",
  },
} as const;

export type User = (typeof USERS)[keyof typeof USERS];

type AuthFixtures = {
  sitePage: Page;
  croPage: Page;
  adminPage: Page;
  patientSitePage: Page;
};

export const test = base.extend<AuthFixtures>({
  sitePage: async ({ page }, use) => {
    await login({
      page,
      user: USERS.site,
    });
    await expect(page).toHaveURL(USERS.site.expectUrl);
    await use(page);
  },

  patientSitePage: async ({ sitePage }, use) => {
    await sitePage.getByRole("link", { name: "Patients", exact: true }).click();
    await expect(sitePage).toHaveURL(/.*\/patients/);
    await use(sitePage);
  },

  croPage: async ({ page }, use) => {
    await login({
      page,
      user: USERS.cro,
    });
    await expect(page).toHaveURL(USERS.cro.expectUrl);
    await use(page);
  },
  adminPage: async ({ page }, use) => {
    await login({
      page,
      user: USERS.admin,
    });
    await expect(page).toHaveURL(USERS.admin.expectUrl);
    await use(page);
  },
});

export { expect };
