"use client";
import {
  ChevronDown,
  Database,
  FileSliders,
  MessageSquare,
  Package,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

import { cn } from "@/lib/utils";

const buildUrl = (href: string, query?: string): string => {
  if (!query) return href;
  return `${href}?${query}`;
};

const isActiveRoute = (pathname: string, href: string) => {
  return pathname === href || pathname.startsWith(href + "/");
};

type SidebarItem = {
  label: string;
  href: string;
  query?: string;
  icon: React.ReactNode;
};
type SidebarParentItem = {
  isParent: true;
  label: string;
  items: SidebarItem[];
  isOpen?: boolean;
};

type SidebarNode =
  | (SidebarItem & {
      isParent?: false;
    })
  | SidebarParentItem;

const SIDE_BAR_MENU: SidebarNode[] = [
  {
    label: "Artifact Categories",
    icon: (
      <Package
        className="text-gray-700 group-[.active]:text-blue-700"
        size={16}
      />
    ),
    href: "/artifact-categories",
    query: "isActive=true&orderBy=documentNumber&orderDirection=desc",
  },
  {
    label: "Reference Models",
    isParent: true,
    isOpen: true,
    items: [
      {
        label: "ISF Reference Models",
        icon: (
          <Database
            className="text-gray-700 group-[.active]:text-blue-700"
            size={16}
          />
        ),
        href: "/reference-models/isf",
      },
      {
        label: "TMF Reference Models",
        icon: (
          <Database
            className="text-gray-700 group-[.active]:text-blue-700"
            size={16}
          />
        ),
        href: "/reference-models/tmf",
      },
    ],
  },
  {
    label: "App Prompts",
    isParent: true,
    isOpen: true,
    items: [
      {
        label: "Prompt Variables",
        icon: (
          <MessageSquare
            className="text-gray-700 group-[.active]:text-blue-700"
            size={16}
          />
        ),
        href: "/app-prompts/prompt-variables",
      },
      {
        label: "Prompt Templates",
        icon: (
          <FileSliders
            className="text-gray-700 group-[.active]:text-blue-700"
            size={16}
          />
        ),
        href: "/app-prompts/prompt-templates",
      },
    ],
  },
];

export const Sidebar = () => {
  return (
    <aside className=" flex h-full w-80 flex-col border-r border-gray-200 bg-white shadow-lg transition-[width] duration-200 ease-in-out dark:border-gray-700">
      <div className="flex-1 overflow-y-auto ">
        <nav className="space-y-2 p-4">
          {SIDE_BAR_MENU.map((item) => {
            if (item.isParent) {
              return <ParentMenuItem key={item.label} {...item} />;
            }
            return <MenuItem key={item.href} {...item} />;
          })}
        </nav>
      </div>

      {/* Footer */}
      <div className="border-t border-gray-200 p-4">
        <div className="text-center text-xs text-gray-700">
          Clincove Config Service
        </div>
      </div>
    </aside>
  );
};

const ParentMenuItem = ({
  label,
  items,
  isOpen,
}: Omit<SidebarParentItem, "isParent">) => {
  const pathname = usePathname();
  const activeParentNode = items.some((child) =>
    isActiveRoute(pathname, child.href),
  );

  return (
    <div className="select-none">
      <button className="peer w-full cursor-pointer rounded-md text-sm font-medium text-slate-500 hover:bg-gray-50">
        <label
          htmlFor={label}
          className="flex items-center justify-between gap-1 px-3 py-2 text-left"
        >
          <span className="flex-1 truncate">{label}</span>
          <input
            defaultChecked={isOpen || activeParentNode}
            id={label}
            type="checkbox"
            hidden
            className="peer hidden"
          />
          <ChevronDown
            size={20}
            className="flex-shrink-0 transition-transform peer-checked:rotate-180"
          />
        </label>
      </button>
      <div className="grid grid-rows-[0fr] transition-[grid-template-rows] duration-300 peer-has-[:checked]:grid-rows-[1fr]">
        <ul className="overflow-hidden">
          {items.map((item) => (
            <MenuItem
              key={item.href}
              href={item.href}
              query={item.query}
              icon={item.icon}
              label={item.label}
            />
          ))}
        </ul>
      </div>
    </div>
  );
};

const MenuItem = ({ href, query, icon, label }: SidebarItem) => {
  const pathname = usePathname();
  const fullHref = buildUrl(href, query);
  const isActive = isActiveRoute(pathname, href);
  return (
    <Link
      href={fullHref}
      className={cn(
        "flex items-center rounded-md px-3 py-2 text-sm transition-colors",
        isActive
          ? "active group bg-blue-100 font-medium text-blue-700"
          : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
      )}
    >
      <span className="mr-3">{icon}</span>
      {label}
    </Link>
  );
};
