import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import React, { useMemo, useState } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { ConfirmModal } from "@/components/ui/modal/confirm-modal";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { GroupProfile } from "@/lib/apis/groups/types";

import { useSite } from "../../../hooks/use-site";
import { generateUserColumns } from "./columns";
import { useUpdateProfileStatus } from "./hooks/use-users-mutations";
import { useUsersByGroupId } from "./hooks/use-users-queries";
import { ModalAddProfile } from "./modal-add-profile";
import { ModalEditProfile } from "./modal-edit-profile";
import StudiesModal from "./studies-modal/studies-modal";

export const UsersTab = () => {
  const params = useParams();
  const id = params.id as string;
  const { data: site } = useSite(id);
  const { data, isPending, isPlaceholderData } = useUsersByGroupId(
    site?.groupId || undefined,
  );
  const { mutateAsync: updateStatus, isPending: isUpdateStatus } =
    useUpdateProfileStatus();
  const [selectedProfile, setSelectedProfile] = useState<GroupProfile | null>(
    null,
  );
  const {
    close: onCloseEditModal,
    isOpen: isOpenEditModal,
    open: onOpenEditModal,
  } = useDisclosure();

  const {
    close: onCloseStudiesModal,
    isOpen: isOpenStudiesModal,
    open: onOpenStudiesModal,
  } = useDisclosure();

  const {
    close: onCloseProfileModal,
    isOpen: isOpenProfileModal,
    open: onOpenProfileModal,
  } = useDisclosure();

  const {
    close: onCloseWarningModal,
    isOpen: isOpenWarningModal,
    open: onOpenWarningModal,
  } = useDisclosure();

  const columns = useMemo(
    () =>
      generateUserColumns({
        onEdit: (user) => {
          onOpenEditModal();
          setSelectedProfile(user);
        },
        onToggleStatus: (user) => {
          if (user.profile.isOnlyActiveProfile && user.profile.isActive) {
            setSelectedProfile(user);
            onOpenWarningModal();
          } else {
            updateStatus({
              userId: user.profile.userId,
              profileId: user.profile.id,
              isActive: !user.profile.isActive,
            });
          }
        },
        onViewStudies: (user) => {
          onOpenStudiesModal();
          setSelectedProfile(user);
        },
      }),
    [updateStatus, onOpenEditModal, onOpenStudiesModal],
  );

  const handleCloseEditModal = () => {
    setSelectedProfile(null);
    onCloseEditModal();
  };

  const handleCloseStudiesModal = () => {
    onCloseStudiesModal();
    setSelectedProfile(null);
  };

  const handleConfirmDisable = () => {
    if (selectedProfile) {
      updateStatus({
        userId: selectedProfile.profile.userId,
        profileId: selectedProfile.profile.id,
        isActive: false,
      });
      setSelectedProfile(null);
      onCloseWarningModal();
    }
  };

  const handleCloseWarningModal = () => {
    setSelectedProfile(null);
    onCloseWarningModal();
  };

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-3 flex items-center justify-between px-6 pt-4">
          <span className="mb-3 text-lg font-semibold dark:text-gray-400">
            Profiles
          </span>
          <Button variant="primary" onClick={onOpenProfileModal}>
            Add Profile
          </Button>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData || isUpdateStatus}>
            <Table columns={columns} data={data?.results ?? []} />
          </LoadingWrapper>
        )}
      </Card>
      {isOpenEditModal && (
        <ModalEditProfile
          isOpen={isOpenEditModal}
          onClose={handleCloseEditModal}
          selectedProfile={selectedProfile as GroupProfile}
        />
      )}
      {isOpenStudiesModal && (
        <StudiesModal
          isOpen={isOpenStudiesModal}
          onClose={handleCloseStudiesModal}
          selectedProfile={selectedProfile as GroupProfile}
          setSelectedProfile={setSelectedProfile}
        />
      )}
      <ModalAddProfile
        isOpen={isOpenProfileModal}
        onClose={onCloseProfileModal}
        groupId={site?.groupId as string}
      />
      <ConfirmModal
        isOpen={isOpenWarningModal}
        onClose={handleCloseWarningModal}
        onConfirm={handleConfirmDisable}
        isLoading={isUpdateStatus}
        title="Disable Profile"
        confirmLabel="Disable"
      >
        <span className="dark:text-white">
          If you disable this profile, the associated user account will also be
          disabled.
        </span>
      </ConfirmModal>
    </>
  );
};
