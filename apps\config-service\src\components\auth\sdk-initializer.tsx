"use client";

import { useAuth } from "@clerk/nextjs";
import { sdkSetURL, sdkSetUser } from "@clincove-eng/backend-sdk-temp";
import { useEffect } from "react";

import { useClerkToken } from "@/contexts/clerk-token";
import { configs } from "@/lib/config";
import { CLERK_TOKEN_TEMPLATE } from "@/lib/constants";

export function SDKInitializer() {
  const { getToken } = useAuth();
  const { setToken, setIsLoaded } = useClerkToken();

  useEffect(() => {
    sdkSetURL(configs.API_URL ?? "");
    getToken({ template: CLERK_TOKEN_TEMPLATE }).then((token) => {
      if (token) {
        sdkSetUser(token);
        setToken(token);
        setIsLoaded(true);
      }
    });
  }, [getToken, setIsLoaded, setToken]);

  return null;
}
