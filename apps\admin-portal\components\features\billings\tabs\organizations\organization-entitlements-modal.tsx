"use client";

import { theme } from "flowbite-react";
import { twMerge } from "tailwind-merge";

import { useGroup } from "@/components/features/settings/groups/hooks/use-group";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Table, TableLoading } from "@/components/ui/table";
import { Group } from "@/lib/apis/groups/types";

import { entitlementsColumns } from "./columns";

type OrganizationEntitlementsModalProps = {
  isOpen: boolean;
  onClose: () => void;
  organization: Group;
};

export const OrganizationEntitlementsModal = ({
  isOpen,
  onClose,
  organization,
}: OrganizationEntitlementsModalProps) => {
  const { data: groupData, isPending: isGroupLoading } = useGroup(
    organization?.id,
  );

  return (
    <WrapperModal
      size="5xl"
      isOpen={isOpen}
      onClose={onClose}
      title={`Entitlements - ${organization.name}`}
      theme={{
        body: {
          base: twMerge(theme.modal.content.base, "p-0"),
        },
      }}
    >
      {isGroupLoading ? (
        <TableLoading columns={entitlementsColumns} />
      ) : (
        <div className="rounded-lg">
          <Table
            columns={entitlementsColumns}
            data={groupData?.entitlements ?? []}
          />
        </div>
      )}
    </WrapperModal>
  );
};
