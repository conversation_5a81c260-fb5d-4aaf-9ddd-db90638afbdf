"use client";

import { Card } from "flowbite-react";

import { TableDataPagination } from "@/components/ui/pagination";
import { Table } from "@/components/ui/table";
import { TableLoading } from "@/components/ui/table/table-loading";

import { useHmacFailuresList } from "../hooks/use-system-health-queries";
import { hmacFailuresColumns } from "./columns";

export const HmacFailuresTable = () => {
  const { data, isPending } = useHmacFailuresList();

  return (
    <Card className="[&>div]:p-0">
      <h2 className="p-4 text-lg font-semibold text-gray-900 dark:text-white">
        HMAC Verification Failures
      </h2>

      {isPending ? (
        <TableLoading columns={hmacFailuresColumns} length={10} />
      ) : (
        <>
          <Table
            data={data?.results || []}
            columns={hmacFailuresColumns}
            manualSorting={false}
          />

          {data?.metadata && <TableDataPagination metadata={data.metadata} />}
        </>
      )}
    </Card>
  );
};
