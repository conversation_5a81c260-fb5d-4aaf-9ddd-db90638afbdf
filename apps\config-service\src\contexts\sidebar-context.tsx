"use client";
import {
  createContext,
  type Props<PERSON>ith<PERSON>hildren,
  useCallback,
  useContext,
  useState,
} from "react";

interface SidebarContextType {
  accordionValue: string[];
  onAccordionValueChange: (value: string | string[]) => void;
}

const SidebarContext = createContext<SidebarContextType | null>(null);

export function SidebarProvider({ children }: PropsWithChildren) {
  const [accordionValue, setAccordionValue] = useState<string[]>([]);

  const onAccordionValueChange = useCallback((value: string | string[]) => {
    const newValue = Array.isArray(value) ? value : [value];
    setAccordionValue(newValue);
  }, []);

  return (
    <SidebarContext.Provider
      value={{
        accordionValue,
        onAccordionValueChange,
      }}
    >
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebarContext() {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error("useSidebarContext must be used within a SidebarProvider");
  }
  return context;
}
