"use client";
import { usePathname } from "next/navigation";
import {
  createContext,
  type PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";

interface SidebarContextType {
  accordionValue: string[];
  onAccordionValueChange: (value: string | string[]) => void;
}

const SidebarContext = createContext<SidebarContextType | null>(null);

export function SidebarProvider({ children }: PropsWithChildren) {
  const pathname = usePathname();

  // Initialize states with empty array to prevent hydration mismatch
  const [accordionValue, setAccordionValue] = useState<string[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Update accordion state after hydration - expand both sections by default
  useEffect(() => {
    if (!isInitialized) {
      // Always expand both Configuration and Reference Models sections by default
      const initialItems: string[] = ["configuration", "reference-models"];

      setAccordionValue(initialItems);
      setIsInitialized(true);
    }
  }, [pathname, isInitialized]);

  const onAccordionValueChange = useCallback((value: string | string[]) => {
    // For multiple accordion, value should always be string[]
    const newValue = Array.isArray(value) ? value : [value];
    setAccordionValue(newValue);
  }, []);

  return (
    <SidebarContext.Provider
      value={{
        accordionValue,
        onAccordionValueChange,
      }}
    >
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebarContext() {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error("useSidebarContext must be used within a SidebarProvider");
  }
  return context;
}
