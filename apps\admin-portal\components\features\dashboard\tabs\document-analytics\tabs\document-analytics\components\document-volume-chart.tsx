"use client";

import { Card } from "flowbite-react";

import { StackedAreaChart } from "@/components/ui/charts/stacked-area";
import { Skeleton } from "@/components/ui/skeleton";
import { DocumentVolumeOverTimeResponse } from "@/lib/apis/document-analytics";
import { formatDate } from "@/lib/utils";

import { useDocumentVolumeOverTime } from "../hooks/use-document-analytics-queries";

type ChartDataPoint = {
  date: string;
  formattedDate: string;
  source: number;
  artifact: number;
  docExchange: number;
};
const transformData = (
  volumeData?: DocumentVolumeOverTimeResponse,
): ChartDataPoint[] => {
  if (
    !volumeData?.artifact.length &&
    !volumeData?.source.length &&
    !volumeData?.docExchange
  )
    return [];
  const data = [
    ...volumeData.source,
    ...volumeData.artifact,
    ...volumeData.docExchange,
  ];
  const dateMap = new Map<string, ChartDataPoint>();

  data.forEach((item) => {
    const date = new Date(item.date).toISOString().split("T")[0];
    const formattedDate = formatDate(item.date, "LLL dd");

    dateMap.set(date, {
      date,
      formattedDate,
      source:
        volumeData.source.find((source) => source.date === item.date)?.count ||
        0,
      artifact:
        volumeData.artifact.find((artifact) => artifact.date === item.date)
          ?.count || 0,
      docExchange:
        volumeData.docExchange.find(
          (docExchange) => docExchange.date === item.date,
        )?.count || 0,
    });
  });

  return Array.from(dateMap.values()).sort((a, b) =>
    a.date.localeCompare(b.date),
  );
};

export const DocumentVolumeChart = () => {
  const { data: volumeData, isPending } = useDocumentVolumeOverTime();

  const chartData = transformData(volumeData);

  if (isPending) {
    return (
      <Card>
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
          Document Volume Over Time
        </h3>
        <Skeleton className="h-80 w-full" />
      </Card>
    );
  }

  return (
    <Card>
      <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
        Document Volume Over Time
      </h3>

      <div className="h-80">
        {chartData.length === 0 ? (
          <div className="flex h-full items-center justify-center">
            <p className="text-xl text-gray-500 dark:text-gray-400">
              No data available
            </p>
          </div>
        ) : (
          <StackedAreaChart
            data={chartData}
            dateKey="formattedDate"
            configs={[
              {
                dataKey: "docExchange",
                name: "DocExchange",
              },
              {
                dataKey: "artifact",
                name: "Artifact",
              },
              {
                dataKey: "source",
                name: "Source",
              },
            ]}
          />
        )}
      </div>
    </Card>
  );
};
