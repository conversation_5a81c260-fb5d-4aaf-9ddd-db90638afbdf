"use client";

import { HiExclamationTriangle } from "react-icons/hi2";

import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import {
  ArtifactCategoriesTab,
  CategoryVersionTab,
  ISFReferenceModelsTab,
  TMFReferenceModelsTab,
} from "./tabs";

const ARTIFACT_TABS = [
  {
    title: "Artifact Category",
    key: "artifact-categories",
    content: <ArtifactCategoriesTab />,
    query:
      "isActive=true&versionLatest=true&orderBy=documentNumber&orderDirection=desc",
  },
  {
    title: "Category Version",
    key: "category-versions",
    content: <CategoryVersionTab />,
  },
  {
    title: "TMF Reference Models",
    key: "tmf-reference-models",
    content: <TMFReferenceModelsTab />,
  },
  {
    title: "ISF Reference Models",
    key: "isf-reference-models",
    content: <ISFReferenceModelsTab />,
  },
];

const BREADCRUMB_ITEMS = [{ label: "Artifact Categories" }];

export const ArtifactCategoryContent = () => {
  return (
    <>
      <Breadcrumb items={BREADCRUMB_ITEMS} />
      <PageHeader>Artifact Categories</PageHeader>
      <div className="flex items-center gap-3 rounded-lg border border-amber-300 bg-amber-100 px-4 py-3 text-sm text-amber-800 dark:border-amber-800/30 dark:bg-amber-900/20 dark:text-amber-300">
        <HiExclamationTriangle className="size-6 flex-shrink-0 text-amber-600 dark:text-amber-400" />
        <span className="font-medium sm:text-lg">
          This configuration is centrally managed and cannot be edited here.
        </span>
      </div>
      <TabsWrapper tabs={ARTIFACT_TABS} />
    </>
  );
};
