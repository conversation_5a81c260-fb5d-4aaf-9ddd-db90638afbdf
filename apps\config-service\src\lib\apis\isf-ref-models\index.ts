import type {
  AddISFRefModelPayload,
  ISFRefModel,
  ISFRefModelListResponse,
  UpdateISFRefModelPayload,
} from "../artifact-categories/types";
import BaseApi from "../base";
import { MetadataParams } from "../types";

class ISFRefModelApi extends BaseApi {
  constructor() {
    super("/artifact-isf-ref-model", true);
  }

  public async create(payload: AddISFRefModelPayload) {
    return this.http.post<ISFRefModel>("/", payload);
  }

  public async list(params?: MetadataParams) {
    const paramUrl = params ? this.generateQueryParams(params) : "";
    return this.http.get<ISFRefModelListResponse>(`?${paramUrl}`);
  }

  public async update(payload: UpdateISFRefModelPayload) {
    return this.http.put<ISFRefModel>(`/${payload.id}`, payload);
  }

  public async delete(id: string) {
    return this.http.delete(`/${id}`);
  }
}

export const isfRefModelApi = new ISFRefModelApi();
export * from "../artifact-categories/types";
