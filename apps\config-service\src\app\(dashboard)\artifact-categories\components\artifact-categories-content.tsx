"use client";

import { Card } from "flowbite-react";
import { ListFilter, Upload } from "lucide-react";
import { useState } from "react";
import { Hi<PERSON><PERSON><PERSON><PERSON>, HiP<PERSON> } from "react-icons/hi";

import { Button } from "@/components/ui/button";
import { usePagination } from "@/hooks/use-pagination";
import { CategoryVersion } from "@/lib/apis/artifact-categories";

import { useFinalizeCategoryVersion } from "../hooks/use-category-version-mutations";
import { useFilterArtifactCategories } from "../hooks/use-filter-artifact-categories";
import { CategoriesTable } from "./categories-table";
import { ModalArtifactCategory } from "./category-modal";
import { CategoryVersionModal } from "./category-version-modal";
import { CustomVersionSelect } from "./custom-version-select";
import { FilterModal } from "./filter-modal";
import { ImportExportActions } from "./import-export-actions";

export const ArtifactCategoriesContent = () => {
  const [isVersionModalOpen, setIsVersionModalOpen] = useState(false);
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [selectedVersion, setSelectedVersion] =
    useState<CategoryVersion | null>(null);
  const [editingVersion, setEditingVersion] = useState<CategoryVersion | null>(
    null,
  );

  const { goToPage } = usePagination();
  const { mutateAsync: finalizeVersion, isPending: isFinalizing } =
    useFinalizeCategoryVersion();

  const {
    tmfZoneName,
    tmfSectionName,
    tmfRecordGroupName,
    isfZoneName,
    isfSectionName,
    isfRecordGroupName,
    recordType,
    alternativeNames,
    isTMF,
    isISF,
    isActive,
    requiresSignature,
    expires,
    inspectableRecord,
    includesPHI,
  } = useFilterArtifactCategories();

  const handleVersionChange = (value: string, option?: CategoryVersion) => {
    setSelectedVersion(option || null);
    goToPage(1);
  };

  const handleAddCategory = () => {
    setSelectedCategory(null);
    setIsCategoryModalOpen(true);
  };

  const handleEditCategory = (category: any) => {
    setSelectedCategory(category);
    setIsCategoryModalOpen(true);
  };

  const handleCloseCategoryModal = () => {
    setIsCategoryModalOpen(false);
    setSelectedCategory(null);
  };

  const handleEditVersion = () => {
    if (selectedVersion) {
      setEditingVersion(selectedVersion);
      setIsVersionModalOpen(true);
    }
  };

  const handlePublishVersion = async () => {
    if (selectedVersion && selectedVersion.status === "draft") {
      await finalizeVersion(selectedVersion.id, {
        onSuccess: (res) => {
          setSelectedVersion(res);
        },
      });
    }
  };

  const handleCloseVersionModal = () => {
    setIsVersionModalOpen(false);
    setEditingVersion(null);
  };

  const appliedFilters = [
    { label: "TMF Zone", value: tmfZoneName, key: "tmfZoneName" },
    { label: "TMF Section", value: tmfSectionName, key: "tmfSectionName" },
    {
      label: "TMF Record Group",
      value: tmfRecordGroupName,
      key: "tmfRecordGroupName",
    },
    { label: "ISF Zone", value: isfZoneName, key: "isfZoneName" },
    { label: "ISF Section", value: isfSectionName, key: "isfSectionName" },
    {
      label: "ISF Record Group",
      value: isfRecordGroupName,
      key: "isfRecordGroupName",
    },
    { label: "Record Type", value: recordType, key: "recordType" },
    {
      label: "Alternative Names",
      value: alternativeNames,
      key: "alternativeNames",
    },
    { label: "TMF", value: isTMF, key: "isTMF" },
    { label: "ISF", value: isISF, key: "isISF" },
    { label: "Active", value: isActive, key: "isActive" },
    {
      label: "Requires Signature",
      value: requiresSignature,
      key: "requiresSignature",
    },
    { label: "Expires", value: expires, key: "expires" },
    {
      label: "Inspectable Record",
      value: inspectableRecord,
      key: "inspectableRecord",
    },
    { label: "Includes PHI", value: includesPHI, key: "includesPHI" },
  ];

  const countedFilters = appliedFilters.filter(
    (filter) =>
      filter.value !== null &&
      filter.value !== undefined &&
      filter.value !== "",
  );

  const handleOpenFilterModal = () => {
    setIsFilterModalOpen(true);
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="mb-2 text-3xl font-bold text-gray-900">
          Artifact Categories
        </h1>
        <p className="text-gray-500">
          Manage artifact categories and their versions for document
          classification.
        </p>
      </div>

      <Card
        className="mb-6"
        theme={{
          root: {
            children: "p-0",
          },
        }}
      >
        <div className="p-6">
          <div className="mb-2 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Category Version
            </h2>
            <div className="flex items-center gap-2">
              {selectedVersion && (
                <>
                  <Button
                    variant="outline"
                    onClick={handleEditVersion}
                    className="flex items-center gap-2"
                  >
                    <HiPencil className="h-4 w-4" />
                    Edit
                  </Button>
                  {selectedVersion.status === "draft" && (
                    <Button
                      variant="primary"
                      onClick={handlePublishVersion}
                      disabled={isFinalizing}
                      isLoading={isFinalizing}
                      className="flex items-center gap-2"
                    >
                      <Upload className="h-4 w-4" />
                      Publish
                    </Button>
                  )}
                </>
              )}
              <Button
                variant="primary"
                onClick={() => setIsVersionModalOpen(true)}
                className="flex items-center gap-2"
              >
                <HiPlus className="h-4 w-4" />
                Add New Version
              </Button>
            </div>
          </div>
          <div className="max-w-md">
            <CustomVersionSelect
              value={selectedVersion?.id || ""}
              onChange={handleVersionChange}
            />
          </div>
        </div>
      </Card>

      <Card
        theme={{
          root: {
            children: "p-0",
          },
        }}
      >
        <div className="">
          <div className="mb-1 flex items-center justify-between p-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Artifact Categories
            </h2>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleOpenFilterModal}
                className="flex items-center gap-2"
              >
                <ListFilter className="h-4 w-4" />
                Filter
                {countedFilters.length > 0 && (
                  <span className="ml-1 flex size-6 items-center justify-center rounded-full bg-blue-600 text-xs font-semibold text-white">
                    {countedFilters.length}
                  </span>
                )}
              </Button>
              <ImportExportActions
                selectedVersion={selectedVersion?.version.toString() || ""}
              />
              <Button
                variant="primary"
                onClick={handleAddCategory}
                disabled={!selectedVersion}
                className="flex items-center gap-2"
              >
                <HiPlus className="h-4 w-4" />
                Add Category
              </Button>
            </div>
          </div>

          {selectedVersion ? (
            <CategoriesTable
              version={selectedVersion}
              onEditCategory={handleEditCategory}
            />
          ) : (
            <div className="rounded-lg bg-gray-50 p-8 text-center">
              <p className="text-gray-500">
                Please select a category version to view and manage artifact
                categories.
              </p>
            </div>
          )}
        </div>
      </Card>

      {/* Modals */}
      {isVersionModalOpen && (
        <CategoryVersionModal
          isOpen
          onClose={handleCloseVersionModal}
          selectedVersion={editingVersion}
          setSelectedVersion={setSelectedVersion}
        />
      )}

      {isCategoryModalOpen && (
        <ModalArtifactCategory
          isOpen
          onClose={handleCloseCategoryModal}
          artifactCategory={selectedCategory}
        />
      )}

      {isFilterModalOpen && (
        <FilterModal
          isOpen={isFilterModalOpen}
          onClose={() => setIsFilterModalOpen(false)}
        />
      )}
    </div>
  );
};
