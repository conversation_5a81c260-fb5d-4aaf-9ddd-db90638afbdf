import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import {
  TableEditButton,
  TableViewButton,
} from "@/components/shared/table-action-buttons";
import { ActiveStatusBadge } from "@/components/ui/badges";
import { IsfTemplate } from "@/lib/apis/isf-templates";
import { formatDate } from "@/lib/utils";

export const generateISFTemplateColumns = ({
  onEdit,
}: {
  onEdit: (template: IsfTemplate) => void;
}): ColumnDef<IsfTemplate>[] => [
  {
    header: "Name",
    accessorKey: "name",
    cell: ({ row }) => (
      <Link
        href={`/isf-templates/${row.original.id}`}
        className="text-primary-500 font-medium hover:underline"
      >
        {row.original.name}
      </Link>
    ),
  },
  {
    header: "Created Date",
    accessorKey: "createdDate",
    cell: ({ row }) =>
      row.original.createdDate
        ? formatDate(row.original.createdDate, "LLL dd, yyyy")
        : "-",
  },
  {
    header: "Status",
    accessorKey: "isActive",
    cell: ({ row }) => <ActiveStatusBadge isActive={row.original.isActive} />,
  },
  {
    header: "Actions",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <TableViewButton
          href={`/isf-templates/${row.original.id}`}
          type="link"
        />
        <TableEditButton onClick={() => onEdit(row.original)} type="button" />
      </div>
    ),
  },
];
