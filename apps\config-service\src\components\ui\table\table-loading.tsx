import { ColumnDef } from "@tanstack/react-table";
import { Table } from "flowbite-react";

interface TableLoadingProps<TData> {
  columns: ColumnDef<TData>[];
  rows?: number;
}

export const TableLoading = <TData,>({
  columns,
  rows = 5,
}: TableLoadingProps<TData>) => {
  return (
    <div className="flex flex-1 flex-col overflow-x-auto">
      <Table>
        <Table.Head>
          {columns.map((column, index) => (
            <Table.HeadCell
              key={index}
              className="whitespace-nowrap !rounded-none border-y p-4 px-6 py-3 font-medium capitalize leading-[18px] text-gray-500"
            >
              {typeof column.header === "string" ? column.header : "Column"}
            </Table.HeadCell>
          ))}
        </Table.Head>
        <Table.Body className="divide-y dark:divide-gray-700">
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <Table.Row
              key={rowIndex}
              className="even:bg-gray-50 dark:even:bg-gray-700/20"
            >
              {columns.map((_, colIndex) => (
                <Table.Cell
                  key={colIndex}
                  className="py-3 font-medium text-gray-500"
                >
                  <div className="h-4 animate-pulse rounded bg-gray-200 dark:bg-gray-600" />
                </Table.Cell>
              ))}
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </div>
  );
};
