"use client";

import { useQuery } from "@tanstack/react-query";
import { ColumnDef } from "@tanstack/react-table";
import { Tooltip } from "flowbite-react";
import { CircleCheckBig, ShieldBan, Tag } from "lucide-react";
import { <PERSON><PERSON><PERSON>ealth } from "react-icons/bi";
import { BsCalendar2XFill } from "react-icons/bs";
import { FaMagnifyingGlass } from "react-icons/fa6";
import { HiPencil, HiTrash } from "react-icons/hi";
import { PiSignature } from "react-icons/pi";

import { TableData, TableDataPagination } from "@/components";
import { Button } from "@/components/ui/button";
import { useSort } from "@/hooks/use-sort";
import {
  artifactCategories,
  ArtifactCategory,
} from "@/lib/apis/artifact-categories";
import { OrderDirection } from "@/lib/apis/types";

import { useDeleteArtifactCategory } from "../hooks/use-artifact-category-mutations";
import { useFilterArtifactCategories } from "../hooks/use-filter-artifact-categories";

interface CategoriesTableProps {
  versionId: string;
  onEditCategory: (category: ArtifactCategory) => void;
}

const orderArtifactCategories = {
  type: (orderDirection: OrderDirection) => [
    {
      orderBy: "tmfRefModel",
      orderDirection,
    },
    {
      orderBy: "isfRefModel",
      orderDirection,
    },
  ],
  documentNumber: (orderDirection: OrderDirection) => [
    {
      orderBy: "tmfZoneNumber",
      orderDirection,
    },
    {
      orderBy: "tmfSectionNumber",
      orderDirection,
    },
    {
      orderBy: "tmfRecordGroupNumber",
      orderDirection,
    },
    {
      orderBy: "isfZoneNumber",
      orderDirection,
    },
    {
      orderBy: "isfSectionNumber",
      orderDirection,
    },
    {
      orderBy: "isfRecordGroupNumber",
      orderDirection,
    },
  ],
  recordGroup: (orderDirection: OrderDirection) => [
    {
      orderBy: "tmfZoneName",
      orderDirection,
    },
    {
      orderBy: "tmfSectionName",
      orderDirection,
    },
    {
      orderBy: "tmfRecordGroupName",
      orderDirection,
    },
    {
      orderBy: "isfZoneName",
      orderDirection,
    },
    {
      orderBy: "isfSectionName",
      orderDirection,
    },
    {
      orderBy: "isfRecordGroupName",
      orderDirection,
    },
  ],
  recordType: (orderDirection: OrderDirection) => [
    {
      orderBy: "recordType",
      orderDirection,
    },
  ],
};

export const CategoriesTable = ({
  versionId,
  onEditCategory,
}: CategoriesTableProps) => {
  const { orderBy, orderDirection } = useSort();
  const { mutate: deleteCategory } = useDeleteArtifactCategory();
  const {
    tmfZoneName,
    tmfSectionName,
    tmfRecordGroupName,
    isfZoneName,
    isfSectionName,
    isfRecordGroupName,
    recordType,
    alternativeNames,
    isTMF,
    isISF,
    isActive,
    requiresSignature,
    expires,
    inspectableRecord,
    includesPHI,
    page,
    take,
  } = useFilterArtifactCategories();

  const params = {
    page: page || undefined,
    take: take || 100,
    filter: {
      tmfZoneName: tmfZoneName || undefined,
      tmfSectionName: tmfSectionName || undefined,
      tmfRecordGroupName: tmfRecordGroupName || undefined,
      isfZoneName: isfZoneName || undefined,
      isfSectionName: isfSectionName || undefined,
      isfRecordGroupName: isfRecordGroupName || undefined,
      recordType: recordType || undefined,
      alternativeNames: alternativeNames || undefined,
      isTMF: typeof isTMF === "boolean" ? isTMF : undefined,
      isISF: typeof isISF === "boolean" ? isISF : undefined,
      version: versionId || undefined,
      isActive: typeof isActive === "boolean" ? isActive : undefined,
      requiresSignature:
        typeof requiresSignature === "boolean" ? requiresSignature : undefined,
      expires: typeof expires === "boolean" ? expires : undefined,
      inspectableRecord:
        typeof inspectableRecord === "boolean" ? inspectableRecord : undefined,
      includesPHI: typeof includesPHI === "boolean" ? includesPHI : undefined,
      orders: orderBy
        ? orderArtifactCategories[
            orderBy as keyof typeof orderArtifactCategories
          ]?.((orderDirection?.toUpperCase() as OrderDirection) ?? "DESC")
        : undefined,
    },
  };

  const { data, isLoading, error, isError } = useQuery({
    queryKey: ["artifact-categories", versionId, params],
    queryFn: () => artifactCategories.list(params),
    enabled: !!versionId,
    placeholderData: (prev) => prev,
  });

  const handleDeleteCategory = (id: string) => {
    deleteCategory(id);
  };

  const columns: ColumnDef<ArtifactCategory>[] = [
    {
      header: "Type",
      accessorKey: "type",
      id: "type",
      cell: ({ row }) => {
        const data = row.original;
        return (
          <div className="flex w-fit flex-col gap-1">
            {data.tmfRefModel?.tmfRefModel && (
              <div className="flex w-full items-center justify-center gap-2 whitespace-nowrap rounded-full border border-blue-700 bg-blue-100 px-2 py-1 font-bold text-blue-700">
                <Tag size={14} />
                <span className="text-xs">
                  TMF - {data.tmfRefModel.tmfRefModel}
                </span>
              </div>
            )}
            {data.isfRefModel?.isfRefModel && (
              <div className="flex w-full items-center justify-center gap-2 whitespace-nowrap rounded-full border border-green-700 bg-green-100 px-2 py-1 text-green-700">
                <Tag size={14} />
                <span className="text-xs">
                  ISF - {data.isfRefModel.isfRefModel}
                </span>
              </div>
            )}
          </div>
        );
      },
    },
    {
      header: "Document Number",
      accessorKey: "documentNumber",
      id: "documentNumber",
      cell: ({ row }) => {
        const data = row.original;
        const tmfNumber =
          data.tmfZoneNumber &&
          data.tmfSectionNumber &&
          data.tmfRecordGroupNumber
            ? `${data.tmfZoneNumber}.${data.tmfSectionNumber}.${data.tmfRecordGroupNumber}`
            : null;
        const isfNumber =
          data.isfZoneNumber &&
          data.isfSectionNumber &&
          data.isfRecordGroupNumber
            ? `${data.isfZoneNumber}.${data.isfSectionNumber}.${data.isfRecordGroupNumber}`
            : null;

        return (
          <button
            className="flex cursor-pointer flex-col text-left hover:underline"
            onClick={() => onEditCategory(data)}
          >
            {tmfNumber && <span>{tmfNumber}</span>}
            {isfNumber && <span>{isfNumber}</span>}
          </button>
        );
      },
    },
    {
      header: "Record Group",
      accessorKey: "recordGroup",
      id: "recordGroup",
      cell: ({ row }) => {
        const data = row.original;
        const tmfGroup =
          data.tmfZoneName && data.tmfSectionName && data.tmfRecordGroupName
            ? `${data.tmfZoneName} / ${data.tmfSectionName} / ${data.tmfRecordGroupName}`
            : null;
        const isfGroup =
          data.isfZoneName && data.isfSectionName && data.isfRecordGroupName
            ? `${data.isfZoneName} / ${data.isfSectionName} / ${data.isfRecordGroupName}`
            : null;

        return (
          <Tooltip content={data.description || "No description available"}>
            <div className="flex cursor-help flex-col">
              {tmfGroup && <span className="text-sm">{tmfGroup}</span>}
              {isfGroup && <span className="text-sm">{isfGroup}</span>}
            </div>
          </Tooltip>
        );
      },
    },
    {
      header: "Record Type",
      accessorKey: "recordType",
      id: "recordType",
      cell: ({ row }) => {
        const data = row.original;
        const alternativeNames = data.alternativeNames;
        const tooltipContent = alternativeNames
          ? `${alternativeNames}`
          : "No alternative names";

        return (
          <Tooltip content={tooltipContent}>
            <span className="cursor-help">{data.recordType}</span>
          </Tooltip>
        );
      },
    },
    {
      header: "Properties",
      id: "properties",
      cell: ({ row }) => {
        const data = row.original;
        return (
          <div className="flex items-center gap-2">
            {data.requiresSignature && (
              <Tooltip content="Requires Signature">
                <PiSignature size={20} className="text-blue-600" />
              </Tooltip>
            )}
            {data.expires && (
              <Tooltip content="Expires">
                <BsCalendar2XFill size={20} className="text-orange-600" />
              </Tooltip>
            )}
            {data.inspectableRecord && (
              <Tooltip content="Inspectable">
                <FaMagnifyingGlass size={20} className="text-purple-600" />
              </Tooltip>
            )}
            {data.includesPHI && (
              <Tooltip content="PHI">
                <BiHealth size={20} className="text-red-600" />
              </Tooltip>
            )}
          </div>
        );
      },
    },
    {
      header: "Status",
      cell: ({ row }) => {
        if (row.original.isActive) {
          return (
            <div className="flex w-fit items-center gap-2 rounded-full bg-green-600 px-3 py-1 text-white">
              <CircleCheckBig size={12} />
              <span className="text-xs">Active</span>
            </div>
          );
        }

        return (
          <div className="flex w-fit items-center gap-2 rounded-full bg-red-700 px-3 py-1 text-white">
            <ShieldBan size={12} />
            <span className="text-xs">Inactive</span>
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const category = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => onEditCategory(category)}
              className="h-8 w-8 p-0"
            >
              <HiPencil className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              onClick={() => handleDeleteCategory(category.id)}
              className="h-8 w-8 border-red-600 p-0 text-red-600 hover:text-red-700"
            >
              <HiTrash className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  if (isError) {
    return (
      <div className="rounded-lg bg-red-50 p-8 text-center">
        <p className="text-red-600">
          Failed to load categories: {error?.message || "Unknown error"}
        </p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-red-700 underline hover:text-red-800"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <TableData
        data={data?.results || []}
        columns={columns}
        isLoading={isLoading}
        enableSorting={true}
      />

      {data?.metadata && <TableDataPagination metadata={data.metadata} />}
    </div>
  );
};
