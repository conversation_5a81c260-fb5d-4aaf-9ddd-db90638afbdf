import { zodResolver } from "@hookform/resolvers/zod";
import React, {
  createContext,
  forwardRef,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import type {
  DefaultValues,
  Mode,
  UseFormProps,
  UseFormReturn,
} from "react-hook-form";
import { FormProvider, useForm } from "react-hook-form";
import type { Except } from "type-fest";
import type { z } from "zod";

import { cn } from "@/lib/utils";

const RequiredFieldsContext = createContext<string[]>([]);

export const useRequiredFields = () => useContext(RequiredFieldsContext);

export type FormActions<TSchema extends z.ZodTypeAny> = {
  formHandler: UseFormReturn<z.TypeOf<TSchema>>;
  isDirty: boolean;
  isValid: boolean;
};

export type FormRef<TSchema extends z.ZodTypeAny> = FormActions<
  z.ZodType<z.infer<TSchema>>
>;

export type FormProps<
  TSchema extends z.ZodTypeAny,
  TDefaultValues extends z.infer<TSchema>,
> = Except<React.InputHTMLAttributes<HTMLFormElement>, "onSubmit"> & {
  schema?: TSchema;
  onSubmit: (
    data: z.infer<TSchema>,
    formHandler?: UseFormReturn<z.TypeOf<TSchema>>,
  ) => void;
  defaultValues?: DefaultValues<TDefaultValues>;
  mode?: Mode;
  isSubmitting?: boolean;
  formProps?: Pick<UseFormProps, "shouldFocusError">;
  formMethods?: UseFormReturn<z.TypeOf<TSchema>>;
};

const Form = forwardRef(
  <TSchema extends z.ZodTypeAny, TDefaultValues extends z.infer<TSchema>>(
    props: FormProps<TSchema, TDefaultValues>,
    ref: React.ForwardedRef<FormActions<TSchema>>,
  ) => {
    const {
      schema,
      children,
      onSubmit,
      mode,
      defaultValues,
      className,
      isSubmitting,
      formProps = { shouldFocusError: false },
      formNoValidate,
      formMethods,
      ...rest
    } = props;

    const internalForm = useForm<z.infer<TSchema>>({
      mode: mode ?? "onBlur",
      resolver: schema && zodResolver(schema),
      defaultValues: useMemo(() => defaultValues, [defaultValues]),
      ...formProps,
    });

    const formHandler = formMethods ?? internalForm;

    const [requiredFields, setRequiredFields] = React.useState<string[]>([]);

    const isValid = formHandler.formState.isValid;

    const isDirty = Object.keys(formHandler.formState.dirtyFields).length > 0;

    useImperativeHandle(ref, () => ({ formHandler, isDirty, isValid }), [
      formHandler,
      isDirty,
      isValid,
    ]);

    useEffect(() => {
      if (schema) {
        const listRequiredFields = Object.entries(
          (schema as any)?.shape ?? (schema as any)?._def?.schema?.shape ?? {},
        )
          .filter(([_, value]) => (value as any)?.isOptional() === false)
          .map(([key]) => key);
        setRequiredFields(listRequiredFields);
      }
    }, [schema]);

    return (
      <FormProvider {...formHandler}>
        <RequiredFieldsContext.Provider value={requiredFields}>
          <form
            {...rest}
            onSubmit={formHandler.handleSubmit((data: z.infer<TSchema>) =>
              onSubmit(data, formHandler),
            )}
            className={cn(
              className,
              !isValid && "is-invalid group",
              !isDirty && "not-dirty group",
              isSubmitting && "is-loading group",
            )}
            noValidate
          >
            {children}
          </form>
        </RequiredFieldsContext.Provider>
      </FormProvider>
    );
  },
);

Form.displayName = "Form";

export { Form };
