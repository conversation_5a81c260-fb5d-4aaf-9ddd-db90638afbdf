import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox, Form, InputField, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import { cn } from "@/lib/utils";
import { capitalize } from "@/utils/string";

import { useFilterVariable } from "../hooks/use-filter-variable";
import { VARIABLE_TYPES } from "./prompt-variable-modal";

const schema = z.object({
  key: z.string().optional(),
  label: z.string().optional(),
  type: z.string().optional(),
  computed: z.boolean().default(false),
  published: z.boolean().default(false),
  // isCacheable: z.boolean().default(false),
  isActive: z.boolean().default(true),
});

type FilterFormValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
};

export const FilterModal = ({ isOpen, onClose }: Props) => {
  const {
    key,
    setKey,
    label,
    setLabel,
    type,
    setType,
    computed,
    setComputed,
    published,
    setPublished,
    isActive,
    setIsActive,
    // isCacheable,
    // setIsCacheable,
    setPage,
  } = useFilterVariable();

  const isClearFilterButtonVisible =
    !!key || !!label || !!type || !!computed || !!published || !!isActive;
  // !!isCacheable;

  const handleSubmit = (data: FilterFormValues) => {
    setKey(data.key || null);
    setLabel(data.label || null);
    setType(data.type || null);
    setComputed(data.computed || null);
    setPublished(data.published || null);
    setIsActive(data.isActive || null);
    // setIsCacheable(data.isCacheable || null);
    setPage(1);
    onClose();
  };

  const handleClearFilters = () => {
    setKey(null);
    setLabel(null);
    setType(null);
    setComputed(null);
    setPublished(null);
    setIsActive(null);
    // setIsCacheable(null);
    setPage(1);
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Filter Prompt Variables</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={schema}
          onSubmit={handleSubmit}
          defaultValues={{
            key: key || "",
            label: label || "",
            type: VARIABLE_TYPES.some((item) => item === type) ? type : "",
            computed: computed || false,
            published: published || false,
            // isCacheable: isCacheable || false,
            isActive: isActive || false,
          }}
        >
          <div className="grid grid-cols-1 gap-4 gap-y-6 sm:grid-cols-2">
            <div className="flex flex-col gap-2">
              <Label htmlFor="key">Key</Label>
              <InputField id="key" name="key" placeholder="Search by key..." />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="label">Label</Label>
              <InputField
                id="label"
                name="label"
                placeholder="Search by label..."
              />
            </div>

            <div className="flex flex-col gap-2 sm:col-span-2">
              <Label htmlFor="type">Type</Label>
              <Select
                id="type"
                name="type"
                placeholder="Select a type"
                options={VARIABLE_TYPES.map((type) => ({
                  label: capitalize(type),
                  value: type,
                }))}
              />
            </div>

            <div className="col-span-2 flex flex-wrap items-center gap-x-8 gap-y-2">
              <div className="flex items-center gap-2">
                <Checkbox id="isActive" name="isActive" />
                <Label htmlFor="isActive">Active</Label>
              </div>
              <div className="flex items-center gap-2">
                <Checkbox id="published" name="published" />
                <Label htmlFor="published">Published</Label>
              </div>

              <div className="flex items-center gap-2">
                <Checkbox id="computed" name="computed" />
                <Label htmlFor="computed">Computed</Label>
              </div>
              {/* <div className="flex items-center gap-2">
                <Checkbox id="isCacheable" name="isCacheable" />
                <Label htmlFor="isCacheable">Cacheable</Label>
              </div> */}
            </div>
          </div>

          <div className="flex flex-col justify-end gap-5 border-none pt-4 sm:flex-row">
            <Button
              onClick={handleClearFilters}
              type="button"
              variant="outline"
              className={cn(!isClearFilterButtonVisible && "hidden")}
            >
              Clear Filters
            </Button>
            <Button type="submit" color="blue">
              Apply Filters
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
