import { useMutation } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

import {
  artifactCategories,
  CreateArtifactCategoryPayload,
  UpdateArtifactCategoryPayload,
} from "@/lib/apis/artifact-categories";

import { artifactCategoriesKeys } from "./use-artifact-categories-queries";

export const useCreateArtifactCategory = () => {
  return useMutation({
    mutationFn: (payload: CreateArtifactCategoryPayload) =>
      artifactCategories.create(payload),
    onSettled: (_, err) => {
      !err && toast.success("Artifact category created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create artifact category");
    },
    meta: {
      awaits: artifactCategoriesKeys.lists(),
    },
  });
};

export const useUpdateArtifactCategory = () => {
  return useMutation({
    mutationFn: ({
      id,
      payload,
    }: {
      id: string;
      payload: UpdateArtifactCategoryPayload;
    }) => artifactCategories.update(id, payload),
    onSettled: (_, err) => {
      !err && toast.success("Artifact category updated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update artifact category");
    },
    meta: {
      awaits: artifactCategoriesKeys.lists(),
    },
  });
};

export const useDeleteArtifactCategory = () => {
  return useMutation({
    mutationFn: (id: string) => artifactCategories.archive(id),
    onSettled: (_, err) => {
      !err && toast.success("Artifact category deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete artifact category");
    },
    meta: {
      awaits: artifactCategoriesKeys.lists(),
    },
  });
};

export const useExportArtifactCategories = () => {
  return useMutation({
    mutationFn: (versionId: string) => artifactCategories.export(versionId),
    onSettled: (_, err) => {
      !err && toast.success("Categories exported successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to export categories");
    },
  });
};

export const useImportArtifactCategories = () => {
  return useMutation({
    mutationFn: ({ formData }: { formData: FormData }) =>
      artifactCategories.import({ formData }),
    onSettled: (_, err) => {
      !err && toast.success("Categories imported successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to import categories");
    },
    meta: {
      awaits: artifactCategoriesKeys.lists(),
    },
  });
};
