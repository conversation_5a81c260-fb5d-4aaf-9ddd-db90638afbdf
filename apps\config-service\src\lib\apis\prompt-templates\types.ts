import type { ListBaseResponse } from "../types";

export const ROLE_MESSAGE_TYPES = ["user", "model"] as const;
export const MODEL_PROVIDERS = [
  {
    label: "Gemini",
    value: "gemini",
  },
  {
    label: "OpenAI",
    value: "openai",
  },
] as const;

export const PROMPT_TYPES = [
  {
    label: "Protocol Explorer",
    value: "protocolExplorer",
  },
  {
    label: "ISF Artifact Category",
    value: "isfArtifactCategory",
  },
  {
    label: "TMF Artifact Category",
    value: "tmfArtifactCategory",
  },
] as const;

type Message = {
  text: string;
};

type ChatHistory = {
  role: (typeof ROLE_MESSAGE_TYPES)[number];
  parts: Message[];
  isCacheable: boolean;
};

export type AddPromptPayload = {
  name: string;
  description?: string;
  modelProvider: (typeof MODEL_PROVIDERS)[number]["value"];
  model: string;
  key: (typeof PROMPT_TYPES)[number]["value"];
  templateContent: ChatHistory[];
  generationConfig?: {
    topK?: number;
    topP?: number;
    temperature?: number;
  };
  promptVariables?: Record<
    string,
    {
      type: string;
      computed: boolean;
      isCacheable: boolean;
    }
  >;
};

export type UpdatePromptPayload = AddPromptPayload & {
  id: string;
};

export type Prompt = AddPromptPayload & {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  isActive: boolean;
  version: number;
};

export type PromptListResponse = ListBaseResponse<Prompt>;
