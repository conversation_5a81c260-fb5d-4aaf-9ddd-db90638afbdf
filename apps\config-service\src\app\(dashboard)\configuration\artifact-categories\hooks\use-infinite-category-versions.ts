import { useInfiniteQuery } from "@tanstack/react-query";

import { artifactCategories } from "@/lib/apis/artifact-categories";

export const USE_INFINITE_CATEGORY_VERSIONS_QUERY_KEY =
  "infinite-category-versions";

export const useInfiniteCategoryVersions = (
  search: string,
  initialPageSize = 50,
) => {
  return useInfiniteQuery({
    queryKey: [USE_INFINITE_CATEGORY_VERSIONS_QUERY_KEY, search],
    queryFn: ({ pageParam = 1 }) =>
      artifactCategories.getVersions({
        page: pageParam,
        take: initialPageSize,
        orderBy: "version",
        orderDirection: "desc",
        filter: { version: search },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};

// Alias to match Admin Portal naming convention
export const useInfiniteCategoryVersion = useInfiniteCategoryVersions;
