import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import {
  AddISFRefModelPayload,
  UpdateISFRefModelPayload,
} from "@/lib/apis/artifact-categories/types";
import { isfRefModelApi } from "@/lib/apis/isf-ref-models";

import { isfRefModelKeys } from "./use-isf-ref-model-queries";

export const useAddISFRefModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: AddISFRefModelPayload) =>
      isfRefModelApi.create(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: isfRefModelKeys.allLists() });
      toast.success("Add ISF Reference Model successfully");
    },
    onError: (err: any) =>
      toast.error(err?.message || "Fail to add ISF Reference Model"),
  });
};

export const useUpdateISFRefModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateISFRefModelPayload) =>
      isfRefModelApi.update(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: isfRefModelKeys.allLists() });
      toast.success("Update ISF Reference Model successfully");
    },
    onError: (err: any) =>
      toast.error(err?.message || "Fail to update ISF Reference Model"),
  });
};

export const useDeleteISFRefModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => isfRefModelApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: isfRefModelKeys.allLists() });
      toast.success("Delete ISF Reference Model successfully");
    },
    onError: (err: any) =>
      toast.error(err?.message || "Fail to delete ISF Reference Model"),
  });
};
