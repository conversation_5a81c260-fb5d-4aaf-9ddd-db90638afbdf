import React from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";

import { useDeletePrompt } from "../hooks/use-protocol-prompt-mutations";

type ExtendedPrompt = {
  id: string;
  name: string;
  isEditing?: boolean;
};

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedPrompt: ExtendedPrompt;
};

export const ConfirmDeleteModal = ({
  isOpen,
  onClose,
  selectedPrompt,
}: Props) => {
  const { mutateAsync: deletePrompt, isPending: isDeleting } =
    useDeletePrompt();

  const handleDelete = async () => {
    await deletePrompt(selectedPrompt.id);
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Delete</Modal.Header>
      <Modal.Body>
        <p className="dark:text-white">
          Are you sure you want to delete{" "}
          <span className="font-semibold">{selectedPrompt.name}</span>?
        </p>
        <div className="mt-4 flex flex-col justify-end gap-4 sm:col-span-2 sm:flex-row">
          <Button onClick={onClose} variant="outline">
            Cancel
          </Button>
          <Button
            onClick={handleDelete}
            color="failure"
            disabled={isDeleting}
            isLoading={isDeleting}
          >
            Delete
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};
