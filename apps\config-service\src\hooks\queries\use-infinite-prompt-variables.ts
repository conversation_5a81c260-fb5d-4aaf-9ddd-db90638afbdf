import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const useInfinitePromptVariables = (
  search?: string,
  filters?: {
    isActive?: boolean;
    published?: boolean;
  },
) => {
  return useInfiniteQuery({
    queryKey: ["prompt-variables", "infinite", search, filters],
    queryFn: ({ pageParam = 1 }) =>
      api.promptVariables.list({
        page: pageParam,
        take: 20,
        filter: {
          ...(search && { key: search }),
          ...filters,
        },
      }),
    getNextPageParam: (lastPage) => {
      const { currentPage, totalPages } = lastPage.metadata;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    initialPageParam: 1,
  });
};
