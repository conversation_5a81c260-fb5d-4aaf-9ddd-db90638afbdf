# Test Documentation

## Running Tests

### Client Portal Tests

```bash
pnpm test-client
```

Runs all client portal E2E tests using Playwright.

### All Tests

```bash
npx playwright test
```

Runs all tests in the `/tests` directory.

### Specific Test Files

```bash
npx playwright test tests/client/auth.spec.ts
npx playwright test tests/client/patient.spec.ts
```

### Test Options

```bash
# Run tests in headed mode (visible browser)
npx playwright test --headed

# Run tests in specific browser
npx playwright test --project=chromium
npx playwright test --project=firefox
npx playwright test --project=webkit

# Run tests with UI mode for debugging
npx playwright test --ui

# Run with trace for detailed debugging
npx playwright test --trace on
```

## Environment Variables

Tests require the following environment variables in your `.env` file:

```env
# Test user credentials
SITE_EMAIL=<EMAIL>
SITE_PASSWORD=password123
CRO_EMAIL=<EMAIL>
CRO_PASSWORD=password123
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=password123

```

## Test Structure

```
tests/
├── admin/          # Admin portal tests
├── client/         # Client portal tests
│   ├── auth.spec.ts    # Authentication flows
│   └── patient.spec.ts # Patient management
├── fixtures/       # Test data
│   └── auth.ts         # User credentials
└── utils/          # Test utilities
    └── auth.ts         # Login helpers
```

## Reports & Debugging

### HTML Reports

Test results are generated as HTML reports. View with:

```bash
npx playwright show-report
```

### Trace Viewer

When tests fail with trace enabled, view detailed traces:

```bash
npx playwright show-trace test-results/[test-name]/trace.zip
```

Traces include:

- Screenshots at each step
- Network requests
- Console logs
- DOM snapshots
