import { useMutation } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

import {
  AddCategoryVersionPayload,
  artifactCategories,
  UpdateCategoryVersionPayload,
} from "@/lib/apis/artifact-categories";

import { USE_INFINITE_CATEGORY_VERSIONS_QUERY_KEY } from "../../../../hooks/queries/use-infinite-category-versions";

export const useCreateCategoryVersion = () => {
  return useMutation({
    mutationFn: (payload: AddCategoryVersionPayload) =>
      artifactCategories.createVersion(payload),
    onSettled: (_, err) => {
      !err && toast.success("Category version created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create category version");
    },
    meta: {
      awaits: [USE_INFINITE_CATEGORY_VERSIONS_QUERY_KEY],
    },
  });
};

export const useUpdateCategoryVersion = () => {
  return useMutation({
    mutationFn: (payload: UpdateCategoryVersionPayload) =>
      artifactCategories.updateVersion(payload),
    onSettled: (_, err) => {
      !err && toast.success("Category version updated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update category version");
    },
    meta: {
      awaits: [USE_INFINITE_CATEGORY_VERSIONS_QUERY_KEY],
    },
  });
};

export const useDeleteCategoryVersion = () => {
  return useMutation({
    mutationFn: (id: string) => artifactCategories.deleteVersion(id),
    onSettled: (_, err) => {
      !err && toast.success("Category version deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete category version");
    },
    meta: {
      awaits: [USE_INFINITE_CATEGORY_VERSIONS_QUERY_KEY],
    },
  });
};

export const useFinalizeCategoryVersion = () => {
  return useMutation({
    mutationFn: (versionId: string) =>
      artifactCategories.finalizeVersion(versionId),
    onSettled: (_, err) => {
      !err && toast.success("Category version published successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to publish category version");
    },
    meta: {
      awaits: [USE_INFINITE_CATEGORY_VERSIONS_QUERY_KEY],
    },
  });
};

export const useGetVersions = () => {
  return useMutation({
    mutationFn: (search: string) =>
      artifactCategories.getVersions({
        filter: {
          version: search,
        },
      }),
  });
};
