import { Tooltip } from "flowbite-react";
import Link from "next/link";
import React from "react";
import { <PERSON><PERSON><PERSON>, HiP<PERSON><PERSON><PERSON>, HiTrash } from "react-icons/hi";

import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

type GeneralProps =
  | {
      type: "button";
      onClick: () => void;
      disabled?: boolean;
      className?: string;
    }
  | {
      type: "link";
      href: string;
      className?: string;
    };

export const TableEditButton = (props: GeneralProps) => {
  const baseClassName = "h-8 w-8 p-0";

  if (props.type === "link") {
    return (
      <Tooltip content="Edit">
        <Link href={props.href} className={cn(baseClassName, props.className)}>
          <Button variant="outline" className={baseClassName}>
            <HiPencil className="h-4 w-4" />
          </Button>
        </Link>
      </Tooltip>
    );
  }

  return (
    <Tooltip content="Edit">
      <Button
        variant="outline"
        onClick={props.onClick}
        disabled={props.disabled}
        className={cn(baseClassName, props.className)}
      >
        <HiPencil className="h-4 w-4" />
      </Button>
    </Tooltip>
  );
};

export const TableViewButton = (props: GeneralProps) => {
  const baseClassName = "h-8 w-8 p-0";

  if (props.type === "link") {
    return (
      <Tooltip content="View">
        <Link href={props.href} className={cn(baseClassName, props.className)}>
          <Button variant="outline" className={baseClassName}>
            <HiEye className="h-4 w-4" />
          </Button>
        </Link>
      </Tooltip>
    );
  }

  return (
    <Tooltip content="View">
      <Button
        variant="outline"
        onClick={props.onClick}
        disabled={props.disabled}
        className={cn(baseClassName, props.className)}
      >
        <HiEye className="h-4 w-4" />
      </Button>
    </Tooltip>
  );
};

type RemoveButtonProps = {
  onClick: () => void;
  label?: string;
  disabled?: boolean;
  className?: string;
};

export const TableRemoveButton = ({
  onClick,
  label = "Delete",
  disabled,
  className,
}: RemoveButtonProps) => {
  return (
    <Tooltip content={label}>
      <Button
        variant="outline"
        onClick={onClick}
        disabled={disabled}
        className={cn(
          "h-8 w-8 border-red-600 p-0 text-red-600 hover:border-red-700 hover:text-red-700",
          className,
        )}
      >
        <HiTrash className="h-4 w-4" />
      </Button>
    </Tooltip>
  );
};

type GenericButtonProps =
  | {
      type: "button";
      onClick: () => void;
      disabled?: boolean;
      className?: string;
      children: React.ReactNode;
      tooltip?: string;
    }
  | {
      type: "link";
      href: string;
      className?: string;
      children: React.ReactNode;
      tooltip?: string;
    };

export const TableGenericButton = (props: GenericButtonProps) => {
  const baseClassName = "h-8 w-8 p-0";
  const tooltip = props.tooltip || "Action";

  if (props.type === "link") {
    return (
      <Tooltip content={tooltip}>
        <Link href={props.href} className={cn(baseClassName, props.className)}>
          <Button variant="outline" className={baseClassName}>
            {props.children}
          </Button>
        </Link>
      </Tooltip>
    );
  }

  return (
    <Tooltip content={tooltip}>
      <Button
        variant="outline"
        onClick={props.onClick}
        disabled={props.disabled}
        className={cn(baseClassName, props.className)}
      >
        {props.children}
      </Button>
    </Tooltip>
  );
};
