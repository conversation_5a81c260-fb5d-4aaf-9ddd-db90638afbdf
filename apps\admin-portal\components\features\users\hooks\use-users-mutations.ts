import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type {
  AddUserPayload,
  UpdateUserPayload,
  UpdateUserStatusPayload,
} from "@/lib/apis/users/types";

import { usersKeys } from "./use-users-queries";

export const useAddUser = () => {
  return useMutation({
    mutationFn: (data: AddUserPayload) => api.users.create(data),
    onSuccess: () => {
      toast.success("User added successfully");
    },
    onError: () => {
      toast.error("Failed to add user");
    },
    meta: { awaits: usersKeys.allLists() },
  });
};

export const useUpdateUser = (id: string) => {
  return useMutation({
    mutationFn: (payload: UpdateUserPayload) => {
      return api.users.update(id, payload);
    },
    onSuccess: () => {
      toast.success("User updated successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
    meta: { awaits: usersKeys.detail(id) },
  });
};

export const useUpdateUserStatus = () => {
  return useMutation({
    mutationFn: (data: UpdateUserStatusPayload & { id: string }) => {
      return api.users.updateStatus(data.id, data);
    },
    onSettled: (_, err, payload) => {
      !err &&
        toast.success(
          `${payload.isActive ? "Enabled" : "Disabled"} user successfully`,
        );
    },
    onError: (error) => {
      toast.error(error.message);
    },
    meta: { awaits: [usersKeys.allLists(), usersKeys.allDetails()] },
  });
};

export const useRevokeUser = () => {
  return useMutation({
    mutationFn: (invitationId: string) =>
      api.users.revokeInvitation(invitationId),
    onSettled: (_, err) =>
      !err && toast.success("Revoke user invitation successfully"),
    onError: (err) =>
      toast.error(err?.message || "Failed to revoke user invitation"),
    meta: { awaits: usersKeys.allInvitations() },
  });
};
