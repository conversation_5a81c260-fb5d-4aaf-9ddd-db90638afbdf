import { useInfiniteQuery } from "@tanstack/react-query";

import { tmfRefModelApi } from "@/lib/apis/tmf-ref-models";

export const useInfiniteTMFRefModels = (
  search: string,
  initialPageSize = 50,
) => {
  return useInfiniteQuery({
    queryKey: ["tmf-ref-models", search],
    queryFn: ({ pageParam = 1 }) =>
      tmfRefModelApi.list({
        page: pageParam,
        take: initialPageSize,
        filter: { tmfRefModel: search },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
