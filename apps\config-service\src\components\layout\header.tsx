"use client";

import { useAuth, useUser } from "@clerk/nextjs";
import { useQueryClient } from "@tanstack/react-query";
import { Navbar } from "flowbite-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { HiChevronDown, HiLogout, HiUser } from "react-icons/hi";

import {
  Dropdown,
  DropdownContent,
  DropdownItem,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { useAuthenticated } from "@/hooks/auth";
import { clearAllStores } from "@/utils/clear-stores";

import { LogoTM } from "../icons/logo-tm";

export function Header() {
  return (
    <Navbar
      fluid
      className="sticky top-0 z-[35] w-full border-b border-gray-200 bg-white px-4 py-2 text-gray-900 shadow-sm"
    >
      <Link href="/" className="flex items-center">
        <LogoTM className="mr-3 h-8 fill-gray-900" width={152} height={27} />
      </Link>

      <UserProfileDropdown />
    </Navbar>
  );
}

export function UserProfileDropdown() {
  const { signOut } = useAuth();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { data } = useAuthenticated();
  const { user } = useUser();

  const handleSignOut = async () => {
    await signOut();
    queryClient.clear();
    clearAllStores();
    router.push("/authentication/sign-in");
  };

  return (
    <Dropdown placement="bottom-end">
      <DropdownTrigger>
        <button className="flex items-center space-x-2 rounded-xl px-3 py-2 text-gray-700 transition-all duration-200 hover:bg-gray-100">
          {user?.imageUrl ? (
            <Image
              src={user.imageUrl}
              alt="User Avatar"
              height={32}
              width={32}
              className="rounded-xl"
            />
          ) : (
            <div className="flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 text-white shadow-sm">
              <HiUser className="h-4 w-4" />
            </div>
          )}
          <HiChevronDown className="h-4 w-4 transition-transform duration-200" />
        </button>
      </DropdownTrigger>

      <DropdownContent className="border border-gray-200 bg-white p-0 shadow-lg">
        <div className="w-80 p-4">
          <div className="flex items-center gap-4">
            {user?.imageUrl ? (
              <Image
                src={user.imageUrl}
                alt="User Avatar"
                width={48}
                height={48}
                className="rounded-xl"
              />
            ) : (
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 text-white shadow-sm">
                <HiUser className="h-6 w-6" />
              </div>
            )}
            <div className="min-w-0 flex-1">
              <p className="truncate text-xs text-gray-500">
                {data?.email || "<EMAIL>"}
              </p>
            </div>
          </div>

          <div className="mt-4 border-t border-gray-200 pt-4">
            <DropdownItem
              onClick={handleSignOut}
              className="flex items-center rounded-lg px-3 py-2 text-sm text-red-600 hover:bg-red-50"
            >
              <HiLogout className="mr-3 h-4 w-4" />
              Sign out
            </DropdownItem>
          </div>
        </div>
      </DropdownContent>
    </Dropdown>
  );
}
