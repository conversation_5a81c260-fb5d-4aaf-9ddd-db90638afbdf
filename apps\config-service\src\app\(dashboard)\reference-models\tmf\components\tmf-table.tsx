"use client";

import { ColumnDef } from "@tanstack/react-table";
import { useMemo } from "react";
import { HiPencil, HiTrash } from "react-icons/hi";

import { TableData, TableDataPagination } from "@/components";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableLoading } from "@/components/ui/table";
import { TMFRefModel } from "@/lib/apis/artifact-categories/types";

import { useDeleteTMFRefModel } from "../hooks/use-tmf-ref-model-mutations";
import { useTMFRefModels } from "../hooks/use-tmf-ref-model-queries";

type Props = {
  onEditTMFModel: (tmfModel: TMFRefModel) => void;
};

const generateTmfRefColumns = ({
  onEdit,
  onDelete,
}: {
  onEdit: (tmfModel: TMFRefModel) => void;
  onDelete: (id: string) => void;
}): ColumnDef<TMFRefModel>[] => [
  {
    header: "ID",
    accessorKey: "id",
    cell: ({ row }) => {
      const data = row.original;
      return <span className="text-sm text-gray-600">{data.id}</span>;
    },
  },
  {
    header: "Label",
    accessorKey: "tmfRefModel",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <button
          className="cursor-pointer text-left hover:underline"
          onClick={() => onEdit(data)}
        >
          <span className="font-medium text-gray-900">{data.tmfRefModel}</span>
        </button>
      );
    },
  },
  {
    header: "Description",
    accessorKey: "description",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <span className="text-gray-600">
          {data.description || "No description"}
        </span>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const tmfModel = row.original;
      return (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => onEdit(tmfModel)}
            className="h-8 w-8 p-0"
          >
            <HiPencil className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            onClick={() => onDelete(tmfModel.id)}
            className="h-8 w-8 border-red-600 p-0 text-red-600 hover:text-red-700"
          >
            <HiTrash className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
];

export const TMFTable = ({ onEditTMFModel }: Props) => {
  const { data, isPending, isPlaceholderData } = useTMFRefModels();
  const { mutate: deleteTMFModel, isPending: isDeleting } =
    useDeleteTMFRefModel();

  const handleDeleteTMFModel = (id: string) => {
    deleteTMFModel(id);
  };

  const columns = useMemo(
    () =>
      generateTmfRefColumns({
        onEdit: onEditTMFModel,
        onDelete: handleDeleteTMFModel,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  return isPending ? (
    <TableLoading columns={columns} />
  ) : (
    <LoadingWrapper isLoading={isDeleting || isPlaceholderData}>
      <TableData data={data?.results || []} columns={columns} />
      {data?.metadata && <TableDataPagination metadata={data.metadata} />}
    </LoadingWrapper>
  );
};
