"use client";

import { ColumnDef } from "@tanstack/react-table";
import { HiPencil, HiTrash } from "react-icons/hi";

import { TableData, TableDataPagination } from "@/components";
import { Button } from "@/components/ui/button";
import { TMFRefModel } from "@/lib/apis/artifact-categories/types";

import { useDeleteTMFRefModel } from "../hooks/use-tmf-ref-model-mutations";
import { useTMFRefModels } from "../hooks/use-tmf-ref-model-queries";

interface TMFTableProps {
  onEditTMFModel: (tmfModel: TMFRefModel) => void;
}

export const TMFTable = ({ onEditTMFModel }: TMFTableProps) => {
  const { data, isPending, isPlaceholderData, error, isError } =
    useTMFRefModels();
  const { mutate: deleteTMFModel } = useDeleteTMFRefModel();

  const handleDeleteTMFModel = (id: string) => {
    deleteTMFModel(id);
  };

  const columns: ColumnDef<TMFRefModel>[] = [
    {
      header: "ID",
      accessorKey: "id",
      cell: ({ row }) => {
        const data = row.original;
        return <span className="text-sm text-gray-600">{data.id}</span>;
      },
    },
    {
      header: "Label",
      accessorKey: "tmfRefModel",
      cell: ({ row }) => {
        const data = row.original;
        return (
          <button
            className="cursor-pointer text-left hover:underline"
            onClick={() => onEditTMFModel(data)}
          >
            <span className="font-medium text-gray-900">
              {data.tmfRefModel}
            </span>
          </button>
        );
      },
    },
    {
      header: "Description",
      accessorKey: "description",
      cell: ({ row }) => {
        const data = row.original;
        return (
          <span className="text-gray-600">
            {data.description || "No description"}
          </span>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const tmfModel = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => onEditTMFModel(tmfModel)}
              className="h-8 w-8 p-0"
            >
              <HiPencil className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              onClick={() => handleDeleteTMFModel(tmfModel.id)}
              className="h-8 w-8 border-red-600 p-0 text-red-600 hover:text-red-700"
            >
              <HiTrash className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  if (isError) {
    return (
      <div className="rounded-lg bg-red-50 p-8 text-center">
        <p className="text-red-600">
          Failed to load TMF Reference Models:{" "}
          {error?.message || "Unknown error"}
        </p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-red-700 underline hover:text-red-800"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <TableData
        data={data?.results || []}
        columns={columns}
        isLoading={isPending || isPlaceholderData}
        enableSorting={false}
      />

      {data?.metadata && <TableDataPagination metadata={data.metadata} />}
    </div>
  );
};
