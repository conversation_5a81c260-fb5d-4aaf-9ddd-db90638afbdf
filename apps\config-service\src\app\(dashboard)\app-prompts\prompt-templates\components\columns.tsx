import { type ColumnDef } from "@tanstack/react-table";
import { FiSearch } from "react-icons/fi";
import { IoMdCopy } from "react-icons/io";
import { MdPowerSettingsNew } from "react-icons/md";

import { ActiveStatusBadge } from "@/components/ui/badges";
import {
  TableGenericButton,
  TableRemoveButton,
  TableViewButton,
} from "@/components/ui/table/table-action-buttons";
import { Prompt } from "@/lib/apis/prompt-templates";

const MODEL_PROVIDER_LABEL = {
  openai: "OpenAi",
  gemini: "Gemini",
};

export const generatePromptColumns = ({
  onActivate,
  onDelete,
  onView,
  onCopy,
  onTest,
}: {
  onView: (data: Prompt) => void;
  onCopy: (data: Prompt) => void;
  onActivate: (data: Prompt) => void;
  onDelete: (data: Prompt) => void;
  onTest: () => void;
}): ColumnDef<Prompt>[] => [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => {
      return (
        <button
          onClick={() => onView(row.original)}
          className="text-primary-500 text-left hover:underline"
        >
          {row.getValue("name")}
        </button>
      );
    },
  },
  {
    header: "Type",
    cell: ({ row }) => {
      return (
        <span className="capitalize">{row.original.key.replace("_", " ")}</span>
      );
    },
  },
  {
    header: "Model Provider",
    cell: ({ row }) => {
      return MODEL_PROVIDER_LABEL[
        row.original.modelProvider as keyof typeof MODEL_PROVIDER_LABEL
      ];
    },
  },
  {
    accessorKey: "model",
    header: "Model",
  },
  {
    accessorKey: "version",
    header: "Version",
  },
  {
    header: "Status",
    cell: ({ row }) => {
      return <ActiveStatusBadge isActive={row.original.isActive} />;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex gap-x-4">
          <TableViewButton type="button" onClick={() => onView(data)} />
          <TableGenericButton
            onClick={() => onCopy(data)}
            type="button"
            tooltip="Copy"
          >
            <IoMdCopy className="size-4" />
          </TableGenericButton>
          <TableGenericButton onClick={onTest} type="button" tooltip="Test">
            <FiSearch className="size-4" />
          </TableGenericButton>
          {!data.isActive && (
            <TableGenericButton
              onClick={() => onActivate(data)}
              type="button"
              tooltip="Activate"
            >
              <MdPowerSettingsNew className="size-4" />
            </TableGenericButton>
          )}
          {!data.isActive && (
            <TableRemoveButton onClick={() => onDelete(data)} />
          )}
        </div>
      );
    },
  },
];
