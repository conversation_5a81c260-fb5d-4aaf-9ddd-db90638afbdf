import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { Monaco } from "@monaco-editor/react";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { But<PERSON> } from "@/components/ui/button";
import {
  Checkbox,
  Form,
  MonacoEditor,
  MonacoEditorInstance,
  Select,
} from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import { useDebounce } from "@/hooks/use-debounce";
import { useInfinitePromptVariables } from "@/hooks/use-infinite-prompt-variables";
import { PromptVariable } from "@/lib/apis/prompt-variables";
import { capitalize } from "@/lib/utils/string";

export const ROLE_MESSAGE_TYPES = ["user", "model"] as const;

const messageSchema = z.object({
  message: z
    .string({
      required_error: "Message is required",
      invalid_type_error: "Message is required",
    })
    .min(1, "Message is required"),
  role: z.enum(ROLE_MESSAGE_TYPES, {
    errorMap: () => ({
      message: "Role is required",
    }),
  }),
  isCacheable: z.boolean().default(false),
});

export type ChatMessage = z.infer<typeof messageSchema>;

export type ChatMessageWithId = ChatMessage & {
  id: number;
};

type AddMessageModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSave: (
    chatMessage: ChatMessage & {
      id?: number;
    },
  ) => void;
  messages: ChatMessage[];
  selectedMessage: ChatMessageWithId | null;
};

export const AddMessageModal = ({
  isOpen,
  onClose,
  onSave,
  messages,
  selectedMessage,
}: AddMessageModalProps) => {
  const [search, setSearch] = useState("");

  const observerTarget = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<MonacoEditorInstance | null>(null);
  const monaco = useRef<Monaco | null>(null);

  const debouncedSearch = useDebounce(search);
  const {
    data,
    isPending,
    isPlaceholderData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfinitePromptVariables(debouncedSearch, {
    isActive: true,
    published: true,
  });

  const formHandler = useForm<ChatMessage>({
    mode: "onChange",
    resolver: zodResolver(messageSchema),
    defaultValues: {
      message: selectedMessage?.message || "",
      role: selectedMessage?.role,
      isCacheable: selectedMessage?.isCacheable || false,
    },
  });

  const isEditing = !!selectedMessage;

  const isDisabledUserRole = !messages.length;

  const validRole = isDisabledUserRole
    ? ROLE_MESSAGE_TYPES.filter((role) => role === "user")
    : ROLE_MESSAGE_TYPES;

  const variables = data?.pages.flatMap((page) => page.results);

  const handleInsertVariable = (variable: PromptVariable) => {
    const position = editorRef.current?.getPosition();
    if (monaco.current && editorRef.current) {
      const formattedKey = `<%= ${variable.key} %>`;
      const range = new monaco.current.Range(
        position?.lineNumber || 1,
        position?.column || 1,
        position?.lineNumber || 1,
        position?.column || 1,
      );
      editorRef.current.executeEdits("insert-variable", [
        {
          range: range,
          text: formattedKey,
          forceMoveMarkers: true,
        },
      ]);

      editorRef.current.setPosition({
        lineNumber: position?.lineNumber || 1,
        column: (position?.column || 1) + formattedKey.length,
      });
      editorRef.current.focus();
    } else {
      const currentMessage = formHandler.getValues("message");
      const isLastCharSpace =
        !currentMessage?.length ||
        currentMessage?.[currentMessage?.length - 1] === " ";
      const formattedKey = isLastCharSpace
        ? `<%= ${variable.key} %>`
        : ` <%= ${variable.key} %>`;
      formHandler.setValue(
        "message",
        currentMessage ? `${currentMessage}${formattedKey}` : formattedKey,
        {
          shouldValidate: true,
        },
      );
    }
  };

  const handleSubmit = (data: z.infer<typeof messageSchema>) => {
    onSave({ ...data, id: selectedMessage?.id });
    onClose();
  };

  const renderVariables = () => {
    if (isPending)
      return Array(5)
        .fill(0)
        .map((_, i) => (
          <div key={i} className="h-8 w-12 animate-pulse rounded bg-gray-300" />
        ));
    if (variables?.length)
      return variables?.map((variable) => (
        <button
          type="button"
          className="rounded-md bg-gray-300 px-2 py-1 text-black dark:bg-gray-500 dark:text-white"
          key={variable.id}
          onClick={() => handleInsertVariable(variable)}
        >
          {variable.key}
        </button>
      ));

    return (
      <div className="flex-1 py-1 text-center text-sm text-gray-500 dark:text-gray-400">
        No variables found
      </div>
    );
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const scrollContainer = scrollContainerRef.current;
      const target = observerTarget.current;

      if (!scrollContainer || !target) return;

      const observer = new IntersectionObserver(
        (entries) => {
          const [entry] = entries;
          if (
            entry.isIntersecting &&
            !isFetchingNextPage &&
            hasNextPage &&
            !debouncedSearch
          ) {
            fetchNextPage();
          }
        },
        {
          root: scrollContainer,
          rootMargin: "0px",
          threshold: 0.1,
        },
      );

      observer.observe(target);

      return () => {
        observer.disconnect();
      };
    }, 300);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [fetchNextPage, hasNextPage, isFetchingNextPage, debouncedSearch]);

  return (
    <Modal show={isOpen} onClose={onClose} size="3xl">
      <Modal.Header>{isEditing ? "Edit Message" : "New Message"}</Modal.Header>
      <Modal.Body>
        <Form
          className="space-y-4"
          onSubmit={handleSubmit}
          formMethods={formHandler}
          schema={messageSchema}
        >
          <div className="space-y-1">
            <Label htmlFor="role">Role</Label>
            <Select
              id="role"
              name="role"
              placeholder="Select a role"
              options={validRole.map((type) => ({
                label: capitalize(type),
                value: type,
              }))}
            />
          </div>
          <div className="space-y-1">
            <Label>Variables</Label>

            <div
              ref={scrollContainerRef}
              className="relative flex max-h-[200px] flex-col overflow-y-auto rounded-md border dark:border-gray-400"
            >
              <div className="sticky top-0 z-10 bg-white p-2 dark:bg-gray-800">
                <input
                  name="search"
                  type="text"
                  placeholder="Search..."
                  className="w-full rounded-md border-gray-300 px-2.5 py-1.5 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                />
              </div>
              <LoadingWrapper isLoading={isPlaceholderData}>
                <div className="flex flex-1 flex-wrap gap-2 p-2">
                  {renderVariables()}
                </div>
              </LoadingWrapper>
              <div ref={observerTarget} className="h-px" aria-hidden="true" />
            </div>
          </div>
          <div className="space-y-1">
            <div className="flex justify-between">
              <Label htmlFor="message">Message</Label>
            </div>
            <MonacoEditor
              options={{
                quickSuggestions: false,
              }}
              height={250}
              language="html"
              name="message"
              editorRef={editorRef}
              monacoRef={monaco}
            />
          </div>

          <div className="flex items-center gap-2">
            <Checkbox id="isCacheable" name="isCacheable" />
            <Label htmlFor="isCacheable">Cacheable</Label>
          </div>

          <div className="flex justify-end gap-4">
            <Button onClick={onClose} variant="outline">
              Cancel
            </Button>
            <Button type="submit" color="blue">
              Save
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
