"use client";

import { useAuth } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import type { PropsWithChildren } from "react";
import { useEffect } from "react";
import { AiOutlineLoading } from "react-icons/ai";
import { CiWarning } from "react-icons/ci";

import { Button } from "@/components/ui/button";
import { useAuthentication } from "@/contexts/authentication";
import { useUserTypeStore } from "@/contexts/user-type";
import { useAuthenticated } from "@/hooks/auth";
import { clearAllStores } from "@/utils/clear-stores";

import { SDKInitializer } from "./sdk-initializer";

export const Authenticate = ({ children }: PropsWithChildren) => {
  const { mutate, isPending } = useAuthenticated();
  const { isLoaded, isSignedIn, signOut } = useAuth();
  const { userType } = useUserTypeStore();
  const router = useRouter();

  useEffect(() => {
    if (!isLoaded) return;
    if (isSignedIn) {
      mutate();
    } else {
      useAuthentication.setState({
        authenticated: false,
        user: undefined,
        error: undefined,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoaded, isSignedIn]);

  const handleCancel = async () => {
    await signOut();
    clearAllStores();
    router.push("/authentication/sign-in");
  };

  const handleGoToClientPortal = async () => {
    let domain = window.location.origin;
    domain = domain.replace("-config", "-app");
    await signOut();
    clearAllStores();

    window.location.href = domain;
  };

  if (isLoaded && userType && userType !== "clincove") {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="mx-4 w-full max-w-md rounded-lg bg-white p-8 shadow-lg dark:bg-gray-800">
          <div className="mb-6 flex items-center justify-center">
            <div className="rounded-full bg-red-100 p-3 dark:bg-red-900">
              <CiWarning className="size-8 text-red-600 dark:text-red-400" />
            </div>
          </div>

          <h2 className="mb-4 text-center text-xl font-semibold text-gray-900 dark:text-white">
            Access Denied
          </h2>

          <p className="mb-8 text-center text-gray-600 dark:text-gray-300">
            You are trying to login to the Config Service with the wrong
            account.
          </p>

          <div className="space-y-3">
            <Button
              className="w-full"
              variant="primary"
              onClick={handleGoToClientPortal}
            >
              Go to Client Portal
            </Button>

            <Button className="w-full" variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!isLoaded || isPending) {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <AiOutlineLoading className="h-10 w-10 animate-spin text-gray-900 dark:text-white" />
      </div>
    );
  }

  return (
    <>
      <SDKInitializer />
      {children}
    </>
  );
};
