"use client";

import { useAuth } from "@clerk/nextjs";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import type { PropsWithChildren } from "react";
import { AiOutlineLoading } from "react-icons/ai";
import { CiWarning } from "react-icons/ci";

import { Button } from "@/components/ui/button";
import { useAuthenticated } from "@/hooks/auth";
import { clearAllStores } from "@/utils/clear-stores";

import { SDKInitializer } from "./sdk-initializer";

export const Authenticate = ({ children }: PropsWithChildren) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { isPending, isError } = useAuthenticated();

  const { isLoaded, signOut } = useAuth();

  const handleCancel = async () => {
    await signOut();
    clearAllStores();
    queryClient.clear();
    router.push("/authentication/sign-in");
  };

  const handleGoToClientPortal = async () => {
    let domain = window.location.origin;
    domain = domain.replace("-config", "-app");
    await signOut({
      redirectUrl: domain,
    });
    queryClient.clear();
    clearAllStores();
  };

  if (!isLoaded || isPending) {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-gray-50">
        <AiOutlineLoading className="h-10 w-10 animate-spin text-gray-900" />
      </div>
    );
  }

  if (isLoaded && isError) {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-gray-50">
        <div className="mx-4 w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
          <div className="mb-6 flex items-center justify-center">
            <div className="rounded-full bg-red-100 p-3">
              <CiWarning className="size-8 text-red-600" />
            </div>
          </div>

          <h2 className="mb-4 text-center text-xl font-semibold text-gray-900">
            Access Denied
          </h2>

          <p className="mb-8 text-center text-gray-600">
            You are trying to login to the Config Service with the wrong
            account.
          </p>

          <div className="space-y-3">
            <Button
              className="w-full"
              variant="primary"
              onClick={handleGoToClientPortal}
            >
              Go to Client Portal
            </Button>

            <Button className="w-full" variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <SDKInitializer />
      {children}
    </>
  );
};
