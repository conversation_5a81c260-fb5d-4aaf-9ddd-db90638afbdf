import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { Monaco } from "@monaco-editor/react";
import { Accordion } from "flowbite-react";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import {
  Checkbox,
  Form,
  MonacoEditor,
  MonacoEditorInstance,
  Select,
} from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import { useInfinitePromptVariables } from "@/hooks/queries/use-infinite-prompt-variables";
import { useDebounce } from "@/hooks/use-debounce";
import { PromptVariable } from "@/lib/apis/prompt-variables";
import { capitalize } from "@/utils/string";

export const ROLE_MESSAGE_TYPES = ["user", "model"] as const;

const messageSchema = z.object({
  message: z
    .string({
      required_error: "Message is required",
      invalid_type_error: "Message is required",
    })
    .min(1, "Message is required"),
  role: z.enum(ROLE_MESSAGE_TYPES, {
    errorMap: () => ({
      message: "Role is required",
    }),
  }),
  isCacheable: z.boolean().default(false),
});

export type ChatMessage = z.infer<typeof messageSchema>;

export type ChatMessageWithId = ChatMessage & {
  id: number;
};

type AddMessageModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSave: (
    chatMessage: ChatMessage & {
      id?: number;
    },
  ) => void;
  messages: ChatMessage[];
  selectedMessage: ChatMessageWithId | null;
};

export const AddMessageModal = ({
  isOpen,
  onClose,
  onSave,
  messages,
  selectedMessage,
}: AddMessageModalProps) => {
  const [search, setSearch] = useState("");

  const observerTarget = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<MonacoEditorInstance | null>(null);
  const monaco = useRef<Monaco | null>(null);

  const debouncedSearch = useDebounce(search);
  const {
    data,
    isPending,
    isPlaceholderData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfinitePromptVariables(debouncedSearch, {
    isActive: true,
    published: true,
  });

  const formHandler = useForm<ChatMessage>({
    mode: "onChange",
    resolver: zodResolver(messageSchema),
    defaultValues: {
      message: selectedMessage?.message || "",
      role: selectedMessage?.role,
      isCacheable: selectedMessage?.isCacheable || false,
    },
  });

  const isEditing = !!selectedMessage;

  const isDisabledUserRole = !messages.length;

  const validRole = isDisabledUserRole
    ? ROLE_MESSAGE_TYPES.filter((role) => role === "user")
    : ROLE_MESSAGE_TYPES;

  const variables = data?.pages.flatMap((page) => page.results);

  const handleInsertVariable = (variable: PromptVariable) => {
    const position = editorRef.current?.getPosition();
    if (monaco.current && editorRef.current) {
      const formattedKey = `<%= ${variable.key} %>`;
      const range = new monaco.current.Range(
        position?.lineNumber || 1,
        position?.column || 1,
        position?.lineNumber || 1,
        position?.column || 1,
      );
      editorRef.current.executeEdits("insert-variable", [
        {
          range: range,
          text: formattedKey,
          forceMoveMarkers: true,
        },
      ]);

      editorRef.current.setPosition({
        lineNumber: position?.lineNumber || 1,
        column: (position?.column || 1) + formattedKey.length,
      });
      editorRef.current.focus();
    } else {
      const currentMessage = formHandler.getValues("message");
      const isLastCharSpace =
        !currentMessage?.length ||
        currentMessage?.[currentMessage?.length - 1] === " ";
      const formattedKey = isLastCharSpace
        ? `<%= ${variable.key} %>`
        : ` <%= ${variable.key} %>`;
      formHandler.setValue(
        "message",
        currentMessage ? `${currentMessage}${formattedKey}` : formattedKey,
        {
          shouldValidate: true,
        },
      );
    }
  };

  const handleSubmit = (data: z.infer<typeof messageSchema>) => {
    onSave({ ...data, id: selectedMessage?.id });
    onClose();
  };

  const renderVariables = () => {
    if (isPending)
      return Array(4)
        .fill(0)
        .map((_, i) => (
          <div
            key={i}
            className="h-6 w-20 animate-pulse rounded bg-gray-200 dark:bg-gray-600"
          />
        ));
    if (variables?.length)
      return variables?.map((variable) => (
        <button
          type="button"
          className="inline-flex items-center rounded-md bg-gray-100 px-2.5 py-1 text-xs font-medium text-gray-700 transition-colors hover:bg-blue-100 hover:text-blue-700 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-blue-900/30 dark:hover:text-blue-300"
          key={variable.id}
          onClick={() => handleInsertVariable(variable)}
        >
          <span className="font-mono">{variable.key}</span>
        </button>
      ));

    return (
      <div className="py-2 text-center text-xs text-gray-500 dark:text-gray-400">
        No variables found
      </div>
    );
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const scrollContainer = scrollContainerRef.current;
      const target = observerTarget.current;

      if (!scrollContainer || !target) return;

      const observer = new IntersectionObserver(
        (entries) => {
          const [entry] = entries;
          if (
            entry.isIntersecting &&
            !isFetchingNextPage &&
            hasNextPage &&
            !debouncedSearch
          ) {
            fetchNextPage();
          }
        },
        {
          root: scrollContainer,
          rootMargin: "0px",
          threshold: 0.1,
        },
      );

      observer.observe(target);

      return () => {
        observer.disconnect();
      };
    }, 300);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [fetchNextPage, hasNextPage, isFetchingNextPage, debouncedSearch]);

  return (
    <Modal show={isOpen} onClose={onClose} size="3xl">
      <Modal.Header>{isEditing ? "Edit Message" : "New Message"}</Modal.Header>
      <Modal.Body>
        <Form
          className="space-y-4"
          onSubmit={handleSubmit}
          formMethods={formHandler}
          schema={messageSchema}
        >
          <div className="space-y-1">
            <Label htmlFor="role">Role</Label>
            <Select
              id="role"
              name="role"
              placeholder="Select a role"
              options={validRole.map((type) => ({
                label: capitalize(type),
                value: type,
              }))}
            />
          </div>
          {/* Variables Section with Accordion */}
          <Accordion>
            <Accordion.Panel>
              <Accordion.Title className="p-2">
                <div className="flex items-center gap-2">
                  <span>Variables</span>
                  <span className="rounded-full bg-gray-100 px-2 py-0.5 text-xs text-gray-600 dark:bg-gray-700 dark:text-gray-400">
                    {variables?.length || 0}
                  </span>
                </div>
              </Accordion.Title>
              <Accordion.Content className="p-2">
                <div className="space-y-3">
                  <div>
                    <input
                      name="search"
                      type="text"
                      placeholder="Search..."
                      className="w-full rounded border border-gray-300 px-2.5 py-1.5 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-400"
                      value={search}
                      onChange={(e) => setSearch(e.target.value)}
                    />
                  </div>

                  <div
                    ref={scrollContainerRef}
                    className="max-h-32 overflow-y-auto"
                  >
                    <LoadingWrapper isLoading={isPlaceholderData}>
                      <div className="flex flex-wrap gap-1.5">
                        {renderVariables()}
                      </div>
                    </LoadingWrapper>
                    <div
                      ref={observerTarget}
                      className="h-px"
                      aria-hidden="true"
                    />
                  </div>
                </div>
              </Accordion.Content>
            </Accordion.Panel>
          </Accordion>
          <div className="space-y-1">
            <div className="flex justify-between">
              <Label htmlFor="message">Message</Label>
            </div>
            <MonacoEditor
              options={{
                quickSuggestions: false,
              }}
              height={250}
              language="html"
              name="message"
              editorRef={editorRef}
              monacoRef={monaco}
            />
          </div>

          <div className="flex items-center gap-2">
            <Checkbox id="isCacheable" name="isCacheable" />
            <Label htmlFor="isCacheable">Cacheable</Label>
          </div>

          <div className="flex justify-end gap-4">
            <Button onClick={onClose} variant="outline">
              Cancel
            </Button>
            <Button type="submit" color="blue">
              Save
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
