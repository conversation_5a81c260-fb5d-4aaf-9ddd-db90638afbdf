import { Page } from "@playwright/test";

import { User } from "../fixtures/auth";

export const SIGN_IN_CLIENT_URL =
  "https://dev-app.us.clincove.com/authentication/sign-in";
export const SIGN_IN_ADMIN_URL =
  "https://dev-admin.us.clincove.com/authentication/sign-in";

export const login = async ({
  page,
  user,
  loginUrl = user.role === "admin" ? SIGN_IN_ADMIN_URL : SIGN_IN_CLIENT_URL,
}: {
  page: Page;
  user: User | Readonly<User>;
  loginUrl?: string;
}) => {
  if (!user.email || !user.password) {
    throw new Error(`Email and password is required`);
  }

  await page.goto(loginUrl);
  await page.getByRole("textbox", { name: "Email address" }).fill(user.email);
  await page.getByRole("button", { name: "Continue" }).click();
  await page.getByRole("textbox", { name: "Password" }).fill(user.password);
  await page.getByRole("button", { name: "Continue" }).click();
};
