import { type ColumnDef } from "@tanstack/react-table";

import { ActiveStatusBadge, PillBadge } from "@/components/ui/badges";
import { TableViewButton } from "@/components/ui/table/table-action-buttons";
import { PromptVariable } from "@/lib/apis/prompt-variables";

const PUBLISHED_VARIANTS = {
  true: {
    value: "Published",
    variant: "success",
  },
  false: {
    value: "Unpublished",
    variant: "default",
  },
} as const;

export const generatePromptVariableColumns = ({
  onView,
}: {
  onView: (data: PromptVariable) => void;
}): ColumnDef<PromptVariable>[] => [
  {
    accessorKey: "key",
    header: "Key",
    cell: ({ row }) => {
      return (
        <button
          onClick={() => onView(row.original)}
          className="text-primary-500 hover:underline"
        >
          {row.getValue("key")}
        </button>
      );
    },
  },
  {
    accessorKey: "label",
    header: "Label",
  },
  {
    header: "Type",
    cell: ({ row }) => {
      return <span className="capitalize">{row.original.type}</span>;
    },
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    header: "Computed",
    cell: ({ row }) => {
      return (
        <span className="font-semibold">
          {row.original.computed ? "Yes" : "No"}
        </span>
      );
    },
  },
  {
    header: "Publication",
    cell: ({ row }) => {
      return (
        <PillBadge
          variant={
            PUBLISHED_VARIANTS[
              `${row.original.published}` as keyof typeof PUBLISHED_VARIANTS
            ].variant as "success" | "default"
          }
        >
          {
            PUBLISHED_VARIANTS[
              `${row.original.published}` as keyof typeof PUBLISHED_VARIANTS
            ].value
          }
        </PillBadge>
      );
    },
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: ({ row }) => {
      return <ActiveStatusBadge isActive={row.original.isActive} />;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex gap-x-4">
          <TableViewButton type="button" onClick={() => onView(data)} />
        </div>
      );
    },
  },
];
