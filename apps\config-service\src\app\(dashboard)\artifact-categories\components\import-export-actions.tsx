"use client";

import { Download, FileUp, Import } from "lucide-react";
import Papa from "papaparse";
import { useRef, useState } from "react";
import { toast } from "react-hot-toast";
import { z } from "zod";

import { <PERSON><PERSON> } from "@/components";
import { CloseButton } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { FileDropzone } from "@/components/ui/form/file-drop-zone";
import { FormRef } from "@/components/ui/form/form";
import { Label } from "@/components/ui/form/label";
import { ConfirmModal } from "@/components/ui/modal/confirm-modal";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { useDisclosure } from "@/hooks/use-disclosure";

import {
  useExportArtifactCategories,
  useImportArtifactCategories,
} from "../hooks/use-artifact-category-mutations";
import { useGetVersions } from "../hooks/use-category-version-mutations";

type Props = {
  selectedVersion: string;
};

const importSchema = z.object({
  file: z.instanceof(File, { message: "Please select a CSV file" }),
});

export const ImportExportActions = ({ selectedVersion }: Props) => {
  const formRef = useRef<FormRef<typeof importSchema>>(null);

  const { mutateAsync: exportCategories, isPending: isExporting } =
    useExportArtifactCategories();
  const { mutateAsync: importCategories, isPending: isImporting } =
    useImportArtifactCategories();
  const { mutateAsync: getVersions, isPending: isPendingGetVersions } =
    useGetVersions();

  const {
    isOpen: isImportModalOpen,
    open: openImportModal,
    close: closeImportModal,
  } = useDisclosure();

  const {
    close: onCloseConfirmCreateNewVersionModal,
    isOpen: isOpenConfirmCreateNewVersionModal,
    open: openConfirmCreateNewVersionModal,
  } = useDisclosure();

  const {
    close: onCloseConfirmExistingVersionModal,
    isOpen: isOpenConfirmExistingVersionModal,
    open: openConfirmExistingVersionModal,
  } = useDisclosure();

  const handleExport = async () => {
    if (!selectedVersion) {
      return toast.error("Please select a version to export");
    }

    try {
      const blob = await exportCategories(selectedVersion);

      const url = window.URL.createObjectURL(new Blob([blob]));
      const link = document.createElement("a");
      link.href = url;
      link.download = `artifact-categories-v${selectedVersion}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error(error);
    }
  };

  const handleImportSubmit = async (data: z.infer<typeof importSchema>) => {
    const selectedFile = data.file;

    Papa.parse(selectedFile, {
      header: true,
      skipEmptyLines: true,
      complete: async (results) => {
        const row = results.data as Record<string, string>[];

        if (!row.length) return toast.error("No data found in the file");

        const uniqueCategoryVersions = new Set(
          row.map((row) => row["category_version"]),
        );

        if (uniqueCategoryVersions.size > 1) {
          return toast.error(
            "Category version mismatch error. All category version ids must be the same.",
          );
        }

        const isMissingCategoryVersion = uniqueCategoryVersions.has("");

        if (isMissingCategoryVersion) {
          return openConfirmCreateNewVersionModal();
        }

        const firstVersion = uniqueCategoryVersions.values().next()
          .value as string;
        const versions = await getVersions(firstVersion);
        const existingVersion = versions?.results.find(
          (version) => version.version.toString() === firstVersion,
        );

        if (existingVersion) {
          return openConfirmExistingVersionModal();
        }

        const formData = new FormData();
        formData.append("file", selectedFile);
        await importCategories({ formData });

        closeImportModal();
      },
      error: (error) => {
        console.error("CSV parsing error:", error);
        toast.error("Error parsing CSV file");
      },
    });
  };

  const handleConfirm = async () => {
    try {
      const formData = new FormData();
      const file = formRef.current?.formHandler.getValues("file") as File;
      formData.append("file", file);

      await importCategories({ formData });

      onCloseConfirmCreateNewVersionModal();
      onCloseConfirmExistingVersionModal();
      closeImportModal();
    } catch (error) {
      console.error(error);
    }
  };

  const handleDownloadTemplate = () => {
    const headers = [
      "recordType",
      "tmfZoneName",
      "tmfSectionName",
      "tmfRecordGroupName",
      "isfZoneName",
      "isfSectionName",
      "isfRecordGroupName",
      "alternativeNames",
      "description",
      "isTMF",
      "isISF",
      "requiresSignature",
      "expires",
      "inspectableRecord",
      "includesPHI",
      "origin",
      "tmfCore",
      "iitStudyArtifacts",
      "isActive",
    ];

    const csvContent = headers.join(",") + "\n";
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "artifact-categories-template.csv";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  return (
    <>
      <Button
        variant="outline"
        onClick={handleExport}
        disabled={!selectedVersion || isExporting}
        isLoading={isExporting}
        className="flex items-center gap-2"
      >
        <FileUp size={18} className="text-gray-500" />
        Export
      </Button>

      <Button
        variant="outline"
        onClick={openImportModal}
        disabled={!selectedVersion}
        className="flex items-center gap-2"
      >
        <Import size={18} className="text-gray-500" />
        Import
      </Button>

      <Button
        variant="outline"
        onClick={handleDownloadTemplate}
        className="flex items-center gap-2"
      >
        <Download size={18} className="text-gray-500" />
        Template
      </Button>

      <WrapperModal
        isOpen={isImportModalOpen}
        onClose={closeImportModal}
        title="Import Artifact Categories"
      >
        <Form
          schema={importSchema}
          ref={formRef}
          onSubmit={handleImportSubmit}
          className="space-y-4"
        >
          <div className="flex flex-col gap-2">
            <Label required htmlFor="file">
              Artifact Categories
            </Label>
            <FileDropzone name="file" acceptTypes={[".csv"]} />
          </div>
          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={closeImportModal} />
            <Button
              type="submit"
              color="blue"
              isLoading={
                (!isOpenConfirmCreateNewVersionModal &&
                  !isOpenConfirmExistingVersionModal &&
                  isImporting) ||
                isPendingGetVersions
              }
            >
              Import
            </Button>
          </div>
        </Form>
      </WrapperModal>

      <ConfirmModal
        isOpen={isOpenConfirmCreateNewVersionModal}
        onClose={onCloseConfirmCreateNewVersionModal}
        onConfirm={handleConfirm}
        isLoading={isImporting}
        title="Confirm Import"
      >
        <span className="dark:text-white">
          Importing this will create a new Category Version. Do you want to
          proceed?
        </span>
      </ConfirmModal>

      <ConfirmModal
        isOpen={isOpenConfirmExistingVersionModal}
        onClose={onCloseConfirmExistingVersionModal}
        onConfirm={handleConfirm}
        isLoading={isImporting}
        title="Confirm Import"
      >
        <span className="dark:text-white">
          This import will append any non duplicated entries to the selected
          category version. Do you want to continue?
        </span>
      </ConfirmModal>
    </>
  );
};
