"use client";
import { type PropsWithChildren, useEffect } from "react";

import { ConfigSidebar } from "@/components/sidebar";

export function LayoutContent({ children }: PropsWithChildren) {
  useEffect(() => {
    document.documentElement.classList.remove("dark");
  }, []);

  return (
    <>
      <div
        id="main-content"
        className="relative z-10 mx-auto h-full w-full overflow-y-auto bg-gray-100 lg:min-h-screen"
      >
        <div className="w-full overflow-auto">
          <div className="w-full">
            <div className="flex h-full w-full">
              <ConfigSidebar />
              <div className="flex h-screen max-w-full flex-1 flex-col pl-[320px]">
                <div className="flex-1">{children}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
