import { <PERSON><PERSON> } from "flowbite-react";
import { useState } from "react";
import { IoMdDownload } from "react-icons/io";
import { MdFileUpload } from "react-icons/md";
import { z } from "zod";

import { LoadingDocument } from "@/components/shared/document-viewers/loading-document";
import { PDFViewer } from "@/components/shared/document-viewers/pdf-viewer";
import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { MonacoEditor } from "@/components/ui/form/monaco-editor";
import { Protocol } from "@/lib/apis/protocols";
import { downloadBlob } from "@/lib/utils";

import { useDownloadProtocol } from "../../hooks/use-protocols-mutations";
import { useProtocol } from "../../hooks/use-protocols-queries";
import { useDownloadedProtocol } from "../../hooks/use-protocols-queries";
import { useAddProtocolContent } from "./hooks/use-protocol-content-mutations";
import { useProtocolContent } from "./hooks/use-protocol-content-queries";
import { ModalUploadProtocol } from "./modal-upload-protocol";

const schema = z.object({
  content: z
    .string({ required_error: "Content is required" })
    .min(1, "Content is required"),
});

type Props = {
  selectedProtocol: Protocol | null;
};

export const ProtocolContentTab = ({ selectedProtocol }: Props) => {
  const { data: content, isLoading: isContentLoading } = useProtocolContent(
    selectedProtocol?.id,
  );

  const { mutateAsync: editContent, isPending } = useAddProtocolContent(
    selectedProtocol?.id,
  );

  async function onSubmit(data: z.infer<typeof schema>) {
    await editContent({
      protocolId: selectedProtocol?.id as string,
      content: data.content,
    });
  }

  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<"pdf" | "editor">("pdf");
  const { data: protocol } = useProtocol(selectedProtocol?.id || "");
  const { mutateAsync: downloadProtocol, isPending: isDownloading } =
    useDownloadProtocol();
  const { data, isPending: isPendingPdf } = useDownloadedProtocol(
    selectedProtocol?.id,
  );
  const handleDownloadProtocol = async (protocolId: string) => {
    const { url } = await downloadProtocol(protocolId);
    const response = await fetch(url);
    const blob = await response.blob();
    downloadBlob(
      blob,
      selectedProtocol?.name ? `${selectedProtocol.name}.pdf` : "protocol.pdf",
    );
  };
  if (!selectedProtocol) return null;

  return (
    <div className="h-full bg-gray-50 dark:bg-gray-900">
      <div className="border-b bg-white px-4 py-4 shadow-sm sm:px-6 dark:border-gray-700 dark:bg-gray-800">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="min-w-0 flex-1">
            <h2 className="text-lg font-bold text-gray-900 sm:text-xl dark:text-white">
              Protocol Content
            </h2>
            {selectedProtocol && (
              <p className="mt-1 truncate text-sm text-gray-600 dark:text-gray-400">
                {selectedProtocol.name}
              </p>
            )}
          </div>
          <div className="flex flex-col gap-2 sm:flex-row sm:gap-3">
            <Button
              disabled={protocol?.isPublished}
              variant="outline"
              size="sm"
              onClick={() => setIsUploadModalOpen(true)}
              className="w-full sm:w-auto"
            >
              <MdFileUpload className="mr-2 h-4 w-4" />
              <span className="sm:inline">Upload PDF</span>
            </Button>
            <Button
              disabled={protocol?.isPublished}
              variant="primary"
              size="sm"
              isLoading={isDownloading}
              onClick={async () =>
                await handleDownloadProtocol(selectedProtocol?.id as string)
              }
              className="w-full sm:w-auto"
            >
              <IoMdDownload className="mr-2 h-4 w-4" />
              <span className="sm:inline">Download PDF</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Tab Navigation */}
      <div className="border-b bg-white lg:hidden dark:border-gray-700 dark:bg-gray-800">
        <nav className="flex">
          <button
            onClick={() => setActiveTab("pdf")}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === "pdf"
                ? "border-b-2 border-blue-500 text-blue-600 dark:text-blue-400"
                : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            }`}
          >
            PDF Viewer
          </button>
          <button
            onClick={() => setActiveTab("editor")}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === "editor"
                ? "border-b-2 border-blue-500 text-blue-600 dark:text-blue-400"
                : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            }`}
          >
            Protocol Editor
          </button>
        </nav>
      </div>

      <div className="mt-4 h-[calc(100vh-210px)] sm:h-[calc(100vh-200px)] lg:grid lg:h-[calc(100vh-200px)] lg:grid-cols-2 lg:gap-4">
        {/* PDF Viewer Section */}
        <div
          className={`overflow-hidden rounded-lg border bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800 ${
            activeTab === "pdf"
              ? "block h-full lg:block"
              : "hidden lg:block lg:h-full"
          }`}
        >
          {isPendingPdf ? (
            <LoadingDocument />
          ) : (
            data?.url && <PDFViewer key={data?.url} url={data?.url} />
          )}
        </div>

        {/* Protocol Editor Section */}
        <div
          className={`flex flex-col overflow-hidden rounded-lg border bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800 ${
            activeTab === "editor"
              ? "flex h-full lg:flex"
              : "hidden lg:flex lg:flex-none"
          }`}
        >
          <div className="border-b px-4 py-3 lg:block dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">
              Protocol Editor
            </h3>
          </div>
          <div className="flex flex-1 flex-col">
            {isContentLoading ? (
              <div className="grid flex-1 place-content-center">
                <Spinner size="xl" color="blue" className="fill-primary-500" />
              </div>
            ) : (
              <Form
                key={selectedProtocol?.id}
                onSubmit={onSubmit}
                schema={schema}
                defaultValues={{
                  content: content?.content ?? "",
                }}
                className="flex flex-1 flex-col"
              >
                <div className="min-h-0 flex-1">
                  <MonacoEditor
                    containerClassName="h-full"
                    height="100%"
                    name="content"
                  />
                </div>
                <div className="border-t p-3 dark:border-gray-700">
                  <div className="flex justify-end">
                    <Button
                      disabled={protocol?.isPublished}
                      isLoading={isPending}
                      variant="primary"
                      type="submit"
                      size="sm"
                    >
                      Save
                    </Button>
                  </div>
                </div>
              </Form>
            )}
          </div>
        </div>
      </div>

      <ModalUploadProtocol
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        selectedProtocol={selectedProtocol}
      />
    </div>
  );
};
