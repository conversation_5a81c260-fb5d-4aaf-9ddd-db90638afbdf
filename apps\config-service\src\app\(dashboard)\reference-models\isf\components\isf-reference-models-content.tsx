"use client";

import { Card } from "flowbite-react";
import { useState } from "react";
import { HiP<PERSON> } from "react-icons/hi";

import { SearchField } from "@/components/shared/search-field";
import { Button } from "@/components/ui/button";
import { ISFRefModel } from "@/lib/apis/artifact-categories/types";

import { ISFModal } from "./isf-modal";
import { ISFTable } from "./isf-table";

export const ISFReferenceModelsContent = () => {
  const [isISFModalOpen, setIsISFModalOpen] = useState(false);
  const [selectedISFModel, setSelectedISFModel] = useState<ISFRefModel | null>(
    null,
  );

  const handleAddISFModel = () => {
    setSelectedISFModel(null);
    setIsISFModalOpen(true);
  };

  const handleEditISFModel = (isfModel: ISFRefModel) => {
    setSelectedISFModel(isfModel);
    setIsISFModalOpen(true);
  };

  const handleCloseISFModal = () => {
    setIsISFModalOpen(false);
    setSelectedISFModel(null);
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="mb-2 text-3xl font-bold text-gray-900">
          ISF Reference Models
        </h1>
        <p className="text-gray-500">
          Manage ISF (Investigator Site File) reference models for document
          classification and organization.
        </p>
      </div>

      <Card
        theme={{
          root: {
            children: "p-0",
          },
        }}
      >
        <div className="">
          <div className="mb-1 flex items-center justify-between p-6">
            <div className="flex w-80 items-center">
              <SearchField placeholder="Search ISF models..." />
            </div>
            <div className="flex items-center gap-4">
              <Button
                variant="primary"
                onClick={handleAddISFModel}
                className="flex items-center gap-2"
              >
                <HiPlus className="h-4 w-4" />
                Add ISF Model
              </Button>
            </div>
          </div>

          <ISFTable onEditISFModel={handleEditISFModel} />
        </div>
      </Card>

      {/* Modal */}
      {isISFModalOpen && (
        <ISFModal
          isOpen
          onClose={handleCloseISFModal}
          selectedISFModel={selectedISFModel}
        />
      )}
    </div>
  );
};
