"use client";

import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  offset,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
} from "@floating-ui/react";
import { useEffect, useMemo, useRef, useState } from "react";
import { HiTrash } from "react-icons/hi";
import { MdOutlineClear } from "react-icons/md";

import { ConfirmModal } from "@/components/ui/modal/confirm-modal";
import { useInfiniteCategoryVersions } from "@/hooks/queries/use-infinite-category-versions";
import { useDebounce } from "@/hooks/use-debounce";
import { CategoryVersion } from "@/lib/apis/artifact-categories";
import { cn } from "@/lib/utils";
import { formatDate } from "@/utils/date";

import { useDeleteCategoryVersion } from "../hooks/use-category-version-mutations";

type Props = {
  value?: string;
  defaultValue?: string;
  onChange?: (value: string, option?: CategoryVersion) => void;
  onClear?: () => void;
  isDisabled?: boolean;
};

export const CustomVersionSelect = ({
  value,
  onChange,
  onClear,
  isDisabled = false,
}: Props) => {
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebounce(search);
  const [isOpen, setIsOpen] = useState(false);
  const [deleteConfirmVersion, setDeleteConfirmVersion] =
    useState<CategoryVersion | null>(null);
  const observerTarget = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const { mutateAsync: deleteVersion, isPending: isDeleting } =
    useDeleteCategoryVersion();

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: (open) => {
      if (!isDisabled) {
        setIsOpen(open);
        if (!open) setSearch("");
      }
    },
    placement: "bottom-start",
    middleware: [
      offset(8),
      flip({
        fallbackPlacements: ["top-start"],
        fallbackStrategy: "bestFit",
        padding: 1,
        crossAxis: false,
      }),
      shift({
        padding: 1,
      }),
      size({
        apply({ rects, elements }) {
          Object.assign(elements.floating.style, {
            width: `${rects.reference.width}px`,
          });
        },
        padding: 1,
      }),
    ],
    whileElementsMounted: autoUpdate,
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    useClick(context),
    useDismiss(context),
  ]);

  const { data, isLoading, isFetchingNextPage, hasNextPage, fetchNextPage } =
    useInfiniteCategoryVersions(debouncedSearch);

  const baseOptions = useMemo(() => {
    if (!data) return [];
    return data.pages.flatMap((page) => page.results);
  }, [data]);

  const selectedOption = baseOptions.find((opt) => opt.id === value);

  useEffect(() => {
    if (!isOpen) return;

    const timeoutId = setTimeout(() => {
      const scrollContainer = scrollContainerRef.current;
      const target = observerTarget.current;

      if (!scrollContainer || !target) return;

      const observer = new IntersectionObserver(
        (entries) => {
          const [entry] = entries;
          if (
            entry.isIntersecting &&
            !isFetchingNextPage &&
            hasNextPage &&
            !debouncedSearch
          ) {
            fetchNextPage();
          }
        },
        {
          root: scrollContainer,
          rootMargin: "0px",
          threshold: 0.1,
        },
      );

      observer.observe(target);

      return () => {
        observer.disconnect();
      };
    }, 300);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [fetchNextPage, hasNextPage, isFetchingNextPage, debouncedSearch, isOpen]);

  const handleSelect = (option: CategoryVersion) => {
    const value = option.id;

    onChange?.(value, option);
    setIsOpen(false);
    setSearch("");
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();

    onChange?.("", undefined);
    onClear?.();
    setIsOpen(false);
    setSearch("");
  };

  const handleDeleteClick = (e: React.MouseEvent, version: CategoryVersion) => {
    e.stopPropagation();
    setDeleteConfirmVersion(version);
  };

  const handleConfirmDelete = async () => {
    if (deleteConfirmVersion) {
      await deleteVersion(deleteConfirmVersion.id);

      if (value === deleteConfirmVersion.id) {
        onChange?.("", undefined);
      }

      setDeleteConfirmVersion(null);
    }
  };

  return (
    <>
      <div className={cn("relative")}>
        <div
          ref={refs.setReference}
          {...getReferenceProps()}
          className="h-full w-full"
        >
          <button
            type="button"
            className={cn(
              "flex w-full items-center justify-between rounded-lg border bg-gray-50 px-2.5 py-2.5 text-left text-sm focus:outline-none focus:ring-2",
              "dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",
              isDisabled
                ? "cursor-not-allowed border-gray-200 bg-gray-50 text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400"
                : "border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-600",
            )}
            disabled={isDisabled}
          >
            <div className="flex min-w-0 flex-1 flex-col items-start">
              {selectedOption ? (
                <span className="font-medium">
                  Version {selectedOption.version}
                </span>
              ) : (
                <span className="text-[#8d94a1]">
                  Choose a category version...
                </span>
              )}
            </div>

            {selectedOption ? (
              <MdOutlineClear
                role="button"
                onClick={handleClear}
                className="size-4 shrink-0"
              />
            ) : (
              <svg
                className={cn(
                  "h-5 w-5 shrink-0 text-gray-400 transition-transform duration-200 dark:text-gray-400",
                  isOpen ? "rotate-180 transform" : "",
                )}
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            )}
          </button>
        </div>

        {isOpen && (
          <FloatingPortal>
            <FloatingFocusManager context={context} modal={false}>
              <div
                ref={refs.setFloating}
                style={floatingStyles}
                {...getFloatingProps()}
                className="z-50 rounded-lg border border-gray-200 bg-white py-1 shadow-lg dark:border-gray-600 dark:bg-gray-700"
              >
                <div className="px-3 pb-2">
                  <input
                    type="text"
                    className="focus:border-primary-500 focus:ring-primary-500 w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-1 dark:border-gray-600 dark:bg-gray-600 dark:text-gray-200 dark:placeholder-gray-400"
                    placeholder="Search versions..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                  />
                </div>
                <div
                  ref={scrollContainerRef}
                  className="select-options max-h-60 overflow-y-auto"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center py-4">
                      <div className="border-t-primary-600 dark:border-t-primary-500 h-5 w-5 animate-spin rounded-full border-2 border-gray-300 dark:border-gray-600" />
                    </div>
                  ) : baseOptions.length > 0 ? (
                    baseOptions.map((option) => {
                      const isSelected = value === option.id;
                      return (
                        <div
                          key={option.id}
                          className={cn(
                            "group flex w-full cursor-pointer items-center justify-between px-3 py-1 text-left text-sm",
                            "hover:bg-gray-100 dark:hover:bg-gray-600",
                            isSelected
                              ? "bg-primary-50 text-primary-600 dark:bg-primary-600/20 dark:text-primary-400"
                              : "text-gray-900 dark:text-gray-200",
                          )}
                          onClick={() => handleSelect(option)}
                        >
                          <div className="flex min-w-0 flex-1 flex-col items-start">
                            <div className="flex w-full items-center justify-between">
                              <p
                                className={cn(
                                  "flex items-center gap-1 font-medium",
                                )}
                              >
                                Version {option.version}{" "}
                                <span
                                  className={cn(
                                    "text-[10px] capitalize",
                                    option.status === "draft"
                                      ? "text-amber-700 dark:text-amber-400"
                                      : "text-green-700 dark:text-green-400",
                                  )}
                                >
                                  {option.status}
                                </span>
                              </p>
                            </div>
                            <span className="mt-0.5 text-xs text-gray-500">
                              Effective: {formatDate(option.effectiveDate)}
                            </span>
                          </div>

                          <button
                            type="button"
                            onClick={(e) => handleDeleteClick(e, option)}
                            disabled={isDeleting}
                            className={cn(
                              "rounded p-1 text-red-600 opacity-0 transition-opacity duration-200 hover:bg-red-100 hover:text-red-700 group-hover:opacity-100",
                              "disabled:cursor-not-allowed disabled:opacity-50",
                              isSelected && "opacity-100",
                            )}
                            title="Delete version"
                          >
                            <HiTrash className="h-4 w-4" />
                          </button>
                        </div>
                      );
                    })
                  ) : (
                    <div className="flex items-center justify-center px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                      No versions found
                    </div>
                  )}
                  <div
                    ref={observerTarget}
                    className="h-px"
                    aria-hidden="true"
                  />
                </div>
              </div>
            </FloatingFocusManager>
          </FloatingPortal>
        )}
      </div>

      {deleteConfirmVersion && (
        <ConfirmModal
          isOpen={true}
          onClose={() => setDeleteConfirmVersion(null)}
          onConfirm={handleConfirmDelete}
          title="Delete Version"
          confirmLabel="Delete"
          isLoading={isDeleting}
        >{`Are you sure you want to delete Version ${deleteConfirmVersion.version}? This action cannot be undone.`}</ConfirmModal>
      )}
    </>
  );
};
