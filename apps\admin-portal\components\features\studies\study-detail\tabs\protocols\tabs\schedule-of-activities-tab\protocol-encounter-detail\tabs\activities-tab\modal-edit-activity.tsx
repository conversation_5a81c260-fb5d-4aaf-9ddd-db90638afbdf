import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox, Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { Skeleton } from "@/components/ui/skeleton";
import { useInfiniteGroups } from "@/hooks/queries/use-infinite-groups";
import { UpdateActivityPayload } from "@/lib/apis/activities";

import { useUpdateEncounterActivity } from "../../hooks/use-update-encounter-activity";

export const schema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  description: z.string({ required_error: "Description is required" }),
  isActive: z.boolean().optional(),
  groupId: z.string({ required_error: "GroupId is required" }),
});

type ModalEditActivityProps = {
  activityId: string;
  isOpen: boolean;
  onClose: () => void;
};

export const ModalEditActivity = function ({
  activityId,
  isOpen,
  onClose,
}: ModalEditActivityProps) {
  const { mutateAsync: updateActivity, isPending: isUpdatingActivity } =
    useUpdateEncounterActivity(activityId);
  async function onSubmit(data: z.infer<typeof schema>) {
    await updateActivity(data as UpdateActivityPayload);
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-4xl">
      <Modal.Header>Edit Activity</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={schema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
        >
          <ActivityForm />
          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              color="blue"
              disabled={isUpdatingActivity}
              isLoading={isUpdatingActivity}
            >
              Save Changes
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export const ActivityForm = () => {
  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
      <div className="space-y-1">
        <Label htmlFor="name">Name</Label>
        <InputField id="name" name="name" placeholder="Enter name..." />
      </div>

      <div className="space-y-1">
        <Label htmlFor="groupId">Group</Label>
        <LazySelect
          name="groupId"
          id="groupId"
          searchPlaceholder="Search groups..."
          useInfiniteQuery={useInfiniteGroups}
          getOptionLabel={(group) => group.name}
          getOptionValue={(group) => group.id}
          params={[]}
          placeholder="Select group"
        />
      </div>

      <div className="space-y-1 sm:col-span-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          placeholder="Enter description..."
        />
      </div>

      <div className="space-y-1">
        <Checkbox id="isActive" name="isActive" />
        <Label htmlFor="isActive">Is Active</Label>
      </div>
    </div>
  );
};

export const ActivityFormSkeleton = () => {
  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
      {/* Name Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-16" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Group ID Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-24" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Description Field - spans 2 columns */}
      <div className="space-y-1 sm:col-span-2">
        <Skeleton className="h-5 w-24" />
        <Skeleton className="h-24 w-full" />
      </div>

      {/* Is Active Field */}
      <div className="col-span-1 flex items-center gap-2">
        <Skeleton className="h-5 w-5" /> {/* Checkbox */}
        <Skeleton className="h-5 w-28" /> {/* Label */}
      </div>
    </div>
  );
};
