"use client";

import { Card } from "flowbite-react";
import { parseAsArrayOf, parseAsString, useQueryState } from "nuqs";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { UncontrolledMultiSelect } from "@/components/ui/form/select/uncontrolled-multi-select";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table } from "@/components/ui/table";
import { TableLoading } from "@/components/ui/table/table-loading";
import { usePagination } from "@/hooks/use-pagination";

import { useProcessingErrorsList } from "../hooks/use-system-health-queries";
import { processingErrorsColumns } from "./columns";

const SOURCE_OPTIONS = [
  { value: "protocol_content", label: "Protocol Content" },
  { value: "source_document_content", label: "Source Document Content" },
  { value: "email_to_source_documents", label: "Email to Source Documents" },
  { value: "email_to_isf", label: "Email to ISF" },
  { value: "email_to_doc_exchange", label: "Email to Doc Exchange" },
];

export const ProcessingErrorsTable = () => {
  const { goToPage } = usePagination();
  const [selectedSources, setSelectedSources] = useQueryState(
    "sources",
    parseAsArrayOf(parseAsString).withDefault([]),
  );

  const { data, isPending, isPlaceholderData } = useProcessingErrorsList();

  return (
    <Card className="[&>div]:p-0">
      <div className="flex items-center justify-between gap-4 p-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          System Processing Errors
        </h2>

        <div className="w-64">
          <UncontrolledMultiSelect
            options={SOURCE_OPTIONS}
            value={selectedSources}
            onChange={(value) => {
              setSelectedSources(value);
              goToPage(1);
            }}
            placeholder="Filter by source..."
          />
        </div>
      </div>
      {isPending ? (
        <TableLoading columns={processingErrorsColumns} length={10} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <Table
            data={data?.results || []}
            columns={processingErrorsColumns}
            manualSorting={false}
          />

          {data?.metadata && <TableDataPagination metadata={data.metadata} />}
        </LoadingWrapper>
      )}
    </Card>
  );
};
