import type {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from "axios";
import axios from "axios";

import { useClerkToken } from "@/contexts/clerk-token";
import { configs } from "@/lib/config";

export type Params = Record<string, unknown>;
export type HttpRequestMethod = "get" | "post" | "put" | "patch" | "delete";

// Create a singleton axios instance with base config
const createAxiosInstance = (baseRoute: string = "") => {
  const instance = axios.create({
    baseURL: `${configs.API_URL}${baseRoute}`,
    withCredentials: true,
    timeout: 60 * 1000, // 60 seconds
  });

  // Add request interceptor to dynamically add the token
  instance.interceptors.request.use((config) => {
    const { token } = useClerkToken.getState();

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  });

  return instance;
};

export class HttpClient {
  public axiosInstance: AxiosInstance;
  private handler: AxiosResponseHandler;

  constructor(baseRoute: string) {
    this.axiosInstance = createAxiosInstance(baseRoute);
    this.setupInterceptors();
    this.handler = new AxiosResponseHandler();
  }

  private setupInterceptors(): void {
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      this.handleResponseError.bind(this),
    );
  }

  private async handleResponseError(error: AxiosError): Promise<AxiosResponse> {
    if (!error.response) {
      return Promise.reject(error);
    }

    return Promise.reject(error);
  }

  private async request<T>(
    method: HttpRequestMethod,
    uri: string,
    params?: Params | FormData,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    try {
      const response = await this.axiosInstance[method](uri, params, config);
      return this.handler.success<T>(response);
    } catch (error) {
      throw this.handler.error(error as AxiosError);
    }
  }

  public async get<T>(uri: string, params: Params = {}): Promise<T> {
    return this.request<T>("get", uri, {
      params,
    });
  }

  public async post<T>(
    uri: string,
    params: Params | FormData = {},
    config: AxiosRequestConfig = {},
  ): Promise<T> {
    return this.request<T>("post", uri, params, config);
  }

  public async put<T>(uri: string, params: Params = {}): Promise<T> {
    return this.request<T>("put", uri, params);
  }

  public async patch<T>(uri: string, params: Params = {}): Promise<T> {
    return this.request<T>("patch", uri, params);
  }

  public async delete<T>(uri: string, params: Params = {}): Promise<T> {
    return this.request<T>("delete", uri, params);
  }
}

class AxiosResponseHandler {
  public success<TData>(res: AxiosResponse): TData {
    return res.data as TData;
  }

  public error(err: AxiosError) {
    if (err.response?.data && typeof err.response.data === "object") {
      return err.response.data;
    }
    return err;
  }
}
