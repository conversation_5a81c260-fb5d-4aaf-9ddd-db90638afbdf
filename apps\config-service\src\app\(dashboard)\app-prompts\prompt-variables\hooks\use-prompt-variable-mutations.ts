import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  AddPromptVariablePayload,
  UpdatePromptVariablePayload,
} from "@/lib/apis/prompt-variables";

import { promptVariableKeys } from "./use-prompt-variable-queries";

export const useAddPromptVariable = () => {
  return useMutation({
    mutationFn: (payload: AddPromptVariablePayload) =>
      api.promptVariables.create(payload),
    onSuccess: () => {
      toast.success("Variable added successfully");
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to add variable");
    },
    meta: {
      awaits: promptVariableKeys.allLists(),
    },
  });
};

export const useUpdatePromptVariable = () => {
  return useMutation({
    mutationFn: (payload: UpdatePromptVariablePayload) =>
      api.promptVariables.update(payload),
    onSuccess: () => {
      toast.success("Variable updated successfully");
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to update variable");
    },
    meta: {
      awaits: promptVariableKeys.allLists(),
    },
  });
};

export const useDeletePromptVariable = () => {
  return useMutation({
    mutationFn: (id: string) => api.promptVariables.delete(id),
    onSuccess: () => {
      toast.success("Variable deleted successfully");
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to delete variable");
    },
    meta: {
      awaits: promptVariableKeys.allLists(),
    },
  });
};
