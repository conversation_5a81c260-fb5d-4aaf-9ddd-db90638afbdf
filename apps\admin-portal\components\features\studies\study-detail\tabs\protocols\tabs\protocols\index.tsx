import { Card } from "flowbite-react/components/Card";
import { useParams } from "next/navigation";
import { Dispatch, SetStateAction, useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table } from "@/components/ui/table";
import { TableLoading } from "@/components/ui/table/table-loading";
import { Protocol } from "@/lib/apis/protocols";

import { generateStudyProtocolColumns } from "../../columns";
import { usePublishProtocol } from "../../hooks/use-protocols-mutations";
import { useStudyProtocols } from "../../hooks/use-protocols-queries";
import { ModalAddProtocol } from "./modal-add-protocol";

type Props = {
  setSelectedProtocol: Dispatch<SetStateAction<Protocol | null>>;
  selectedProtocol: Protocol | null;
};

export const ProtocolsTab = ({
  selectedProtocol,
  setSelectedProtocol,
}: Props) => {
  const [isOpen, setIsOpen] = useState(false);
  const { id } = useParams();

  const studyId = id as string;

  const {
    data: protocols,
    isPending,
    isPlaceholderData,
  } = useStudyProtocols(studyId);
  const { mutateAsync: publish } = usePublishProtocol(studyId);

  const handlePublish = (protocolId: string) => {
    if (!studyId) return;
    publish(protocolId);
  };

  const columns = useMemo(
    () =>
      generateStudyProtocolColumns({
        onPublish: (id) => handlePublish(id),
        onSelect: (protocol) => setSelectedProtocol(protocol),
        selectedProtocol,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [selectedProtocol, setSelectedProtocol],
  );

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">Protocol Versions</div>
          <Button variant="primary" onClick={() => setIsOpen(true)}>
            <IoMdAdd />
            Add Protocol
          </Button>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} data={protocols?.results ?? []} />
            {protocols?.metadata && (
              <TableDataPagination metadata={protocols.metadata} />
            )}
          </LoadingWrapper>
        )}
      </Card>
      <ModalAddProtocol isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </>
  );
};
