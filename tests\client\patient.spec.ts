import { expect, test } from "../fixtures/auth";

const PATIENT_TEST_ID = "TEST-001";

test.setTimeout(60000);

test.describe("Patient Page", () => {
  test("Site user can view patient detail", async ({ patientSitePage }) => {
    await patientSitePage.waitForSelector("table");
    const dataRows = patientSitePage.locator("table tbody tr");
    const rowCount = await dataRows.count();

    if (rowCount === 0) {
      await expect(
        patientSitePage.getByRole("heading", {
          name: "No patients added yet",
        }),
      ).toBeVisible();

      return;
    }

    const randomRowIndex = Math.floor(Math.random() * rowCount) + 1;

    await patientSitePage
      .locator(`table tbody tr:nth-child(${randomRowIndex}) td:first-child a`)
      .click();
    await expect(patientSitePage).toHaveURL(/\/patients\/.+$/);
  });

  test("Site user can create a new patient", async ({ patientSitePage }) => {
    await patientSitePage.getByRole("button", { name: "New Patient" }).click();
    await expect(
      patientSitePage.getByRole("heading", { name: "Add New Patient" }),
    ).toBeVisible();
    await patientSitePage.getByRole("textbox", { name: "First Name" }).click();
    await patientSitePage
      .getByRole("textbox", { name: "First Name*" })
      .fill("John");
    await patientSitePage.getByRole("textbox", { name: "Last Name" }).click();
    await patientSitePage
      .getByRole("textbox", { name: "Last Name*" })
      .fill("Doe");
    await patientSitePage.getByRole("textbox", { name: "Patient ID" }).click();
    await patientSitePage
      .getByRole("textbox", { name: "Patient ID" })
      .fill(PATIENT_TEST_ID);
    await patientSitePage
      .getByRole("button", { name: "Select Status" })
      .click();
    await patientSitePage
      .locator("div[data-floating-ui-focusable] div button")
      .first()
      .click();
    await patientSitePage
      .getByRole("button", { name: "Select Study", exact: true })
      .click();
    await patientSitePage
      .locator("div[data-floating-ui-focusable] div button")
      .first()
      .click();
    await patientSitePage.getByRole("button", { name: "Select Sex" }).click();

    await patientSitePage
      .locator("div[data-floating-ui-focusable] div button")
      .first()
      .click();
    await patientSitePage.getByText("yyyy/mm/dd").click();
    await patientSitePage
      .getByRole("button", { name: "Today", exact: true })
      .click();
    await patientSitePage
      .getByRole("heading", { name: "Add New Patient" })
      .click();
    await patientSitePage.getByRole("button", { name: "Save" }).click();
    await expect(
      patientSitePage.getByText("Patient created successfully"),
    ).toBeVisible();
  });

  test("Site user can search and edit a patient", async ({
    patientSitePage,
  }) => {
    await patientSitePage.locator("form div").nth(1).click();
    await patientSitePage.getByPlaceholder("Search").click();
    await patientSitePage.getByPlaceholder("Search").fill(PATIENT_TEST_ID);

    await patientSitePage.waitForTimeout(3000);
    const dataRows = patientSitePage.locator("table tbody tr");
    const rowCount = await dataRows.count();

    if (rowCount === 0) {
      await expect(
        patientSitePage.getByRole("heading", {
          name: "No patients added yet",
        }),
      ).toBeVisible();

      return;
    }

    const randomRowIndex = Math.floor(Math.random() * rowCount) + 1;

    await patientSitePage
      .locator(
        `table tbody tr:nth-child(${randomRowIndex}) td:last-child button`,
      )
      .click();
    await patientSitePage.getByRole("menuitem", { name: "Edit" }).click();
    await expect(
      patientSitePage.getByRole("heading", { name: "Edit Patient" }),
    ).toBeVisible();
    await patientSitePage.getByRole("textbox", { name: "Patient ID*" }).click();
    await patientSitePage
      .getByRole("textbox", { name: "Patient ID*" })
      .fill("TEST-002");
    await patientSitePage.getByRole("button", { name: "Save" }).click();
    await expect(
      patientSitePage.getByText("Patient updated successfully"),
    ).toBeVisible();
  });

  test("Site user can remove a patient", async ({ patientSitePage }) => {
    await patientSitePage.locator("form div").nth(1).click();
    await patientSitePage.getByPlaceholder("Search").click();
    await patientSitePage.getByPlaceholder("Search").fill("test");

    await patientSitePage.waitForTimeout(3000);
    const dataRows = patientSitePage.locator("table tbody tr");
    const rowCount = await dataRows.count();

    if (rowCount === 0) {
      await expect(
        patientSitePage.getByRole("heading", {
          name: "No patients added yet",
        }),
      ).toBeVisible();

      return;
    }

    const randomRowIndex = Math.floor(Math.random() * rowCount) + 1;

    await patientSitePage
      .locator(
        `table tbody tr:nth-child(${randomRowIndex}) td:last-child button`,
      )
      .click();
    await patientSitePage.getByRole("menuitem", { name: "Edit" }).click();
    await expect(
      patientSitePage.getByRole("heading", { name: "Edit Patient" }),
    ).toBeVisible();
    await patientSitePage
      .getByRole("button", { name: "Delete Patient" })
      .click();
    await expect(
      patientSitePage.getByRole("heading", { name: "Delete Patient" }),
    ).toBeVisible();
    await patientSitePage.getByRole("button", { name: "Delete" }).click();
    await expect(
      patientSitePage.getByText("Patient deleted successfully"),
    ).toBeVisible();
  });
});
