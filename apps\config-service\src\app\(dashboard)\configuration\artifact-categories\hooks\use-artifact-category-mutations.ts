import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

import {
  artifactCategories,
  CreateArtifactCategoryPayload,
  UpdateArtifactCategoryPayload,
} from "@/lib/apis/artifact-categories";

export const useCreateArtifactCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: CreateArtifactCategoryPayload) =>
      artifactCategories.create(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["artifact-categories"] });
      toast.success("Artifact category created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create artifact category");
    },
  });
};

export const useUpdateArtifactCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      payload,
    }: {
      id: string;
      payload: UpdateArtifactCategoryPayload;
    }) => artifactCategories.update(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["artifact-categories"] });
      toast.success("Artifact category updated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update artifact category");
    },
  });
};

export const useDeleteArtifactCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => artifactCategories.archive(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["artifact-categories"] });
      toast.success("Artifact category deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete artifact category");
    },
  });
};
