"use client";

import {
  matchQuery,
  MutationCache,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import type { PropsWithChildren } from "react";
import { useState } from "react";

import { isArrayOfQueryKeys } from "@/utils/react-query";

export const Providers = ({ children }: PropsWithChildren) => {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1 minute
            retry: 1,
          },
        },
        mutationCache: new MutationCache({
          onSuccess: async (_data, _variables, _context, mutation) => {
            if (mutation.meta?.invalidates) {
              const invalidateKeys = isArrayOfQueryKeys(
                mutation.meta.invalidates,
              )
                ? mutation.meta.invalidates
                : [mutation.meta.invalidates];

              queryClient.invalidateQueries({
                predicate: (query) =>
                  invalidateKeys.some((queryKey) =>
                    matchQuery({ queryKey }, query),
                  ) ?? false,
              });
            }

            if (mutation.meta?.awaits) {
              const awaitKeys = isArrayOfQueryKeys(mutation.meta.awaits)
                ? mutation.meta.awaits
                : [mutation.meta.awaits];
              await queryClient.invalidateQueries({
                predicate: (query) =>
                  awaitKeys.some((queryKey) =>
                    matchQuery({ queryKey }, query),
                  ) ?? false,
              });
            }
          },
        }),
      }),
  );

  return (
    <QueryClientProvider client={queryClient}>
      <NuqsAdapter>{children}</NuqsAdapter>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
};
